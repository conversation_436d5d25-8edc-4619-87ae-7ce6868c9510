# Closes any issues that no longer have user interaction
name: 🎯 Close Stale Issues

on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 * * *' # Runs every day at midnight UTC

jobs:
  stale:
    runs-on: ubuntu-latest

    steps:
      # Comment on, then close issues that haven't been updated for ages 
      - name: Close Stale Issues
        uses: actions/stale@v4
        with:
          repo-token: ${{ secrets.BOT_GITHUB_TOKEN || secrets.GITHUB_TOKEN }}
          days-before-stale: 183
          days-before-close: 7
          operations-per-run: 30
          remove-stale-when-updated: true
          enable-statistics: true
          stale-issue-message: > 
            This issue has gone 6 months without an update. To keep the ticket open, please indicate that it is still relevant in a comment below.
            Otherwise it will be closed in 7 days.
          stale-pr-message: >
            This PR is stale because it has been open 6 months with no activity. Either remove the stale label or comment below with a short update,
            otherwise this PR will be closed in 7 days.
          close-issue-message: >
            This issue was automatically closed because it has been stalled for over 6 months with no activity.
          close-pr-message: >
            This pull request was automatically closed because it has been stalled for over 6 months with no activity.
          stale-issue-label: '⚰️ Stale'
          close-issue-label: '🕸️ Inactive'
          stale-pr-label: '⚰️ Stale'
          close-pr-label: '🕸️ Inactive'
          exempt-issue-labels: '📌 Keep Open'
          exempt-pr-labels: '📌 Keep Open'
          labels-to-add-when-unstale: '📌 Keep Open'
          
        # Comment on, then close issues that required a response from the user, but didn't get one
      - name: Close Issues without Response
        uses: actions/stale@v4
        with:
          repo-token: ${{ secrets.BOT_GITHUB_TOKEN || secrets.GITHUB_TOKEN }}
          days-before-stale: 7
          days-before-close: 7
          operations-per-run: 30
          remove-stale-when-updated: true
          stale-issue-message: >
            Hi! Looks like additional info is required for this issue to be addressed.
            Don't forget to provide this within the next few days to keep your ticket open.
          close-issue-message: 'Issue closed due to no response from user.'
          only-labels: '🚏 Awaiting User Response'
          labels-to-remove-when-unstale: '🚏 Awaiting User Response, 🛑 No Response'
          stale-issue-label: '🛑 No Response'
          close-issue-label: '🕸️ Inactive'
          exempt-issue-labels: '📌 Keep Open'
          exempt-pr-labels: '📌 Keep Open'
          
        # Comment on issues that we should have replied to
      - name: Notify Repo Owner to Respond
        uses: actions/stale@v4
        with:
          repo-token: ${{ secrets.BOT_GITHUB_TOKEN || secrets.GITHUB_TOKEN }}
          days-before-stale: 7
          days-before-close: 183
          operations-per-run: 30
          remove-stale-when-updated: true
          stale-issue-message: Hey SillyTavern, - Don't forget to respond!
          stale-pr-message: Hey SillyTavern, - Don't forget to respond!
          only-labels: '👤 Awaiting Maintainer Response'
          labels-to-remove-when-unstale: '👤 Awaiting Maintainer Response'
          close-issue-message: 'Closed due to no response from repo author for over a year'
          close-pr-message: 'Closed due to no response from repo author for over a year'
          stale-issue-label: '👤 Awaiting Maintainer Response'
          stale-pr-label: '👤 Awaiting Maintainer Response'
          close-issue-label: '🕸️ Inactive'
          close-pr-label: '🕸️ Inactive'
          exempt-issue-labels: '📌 Keep Open'
          exempt-pr-labels: '📌 Keep Open'
