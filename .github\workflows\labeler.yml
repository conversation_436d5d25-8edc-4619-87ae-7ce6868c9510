name: "Issue Labeler"
on:
  issues:
    types: [opened, edited]

permissions:
  issues: write
  contents: read

jobs:
  triage:
    runs-on: ubuntu-latest
    steps:
    - uses: github/issue-labeler@v3.4
      with:
        configuration-path: .github/labeler.yml
#        not-before: 2020-01-15T02:54:32Z # optional and will result in any issues prior to this timestamp to be ignored.
        enable-versioned-regex: 0
        repo-token: ${{ github.token }}