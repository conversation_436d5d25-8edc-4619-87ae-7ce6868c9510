# When a new comment is added to an issue, if it had the Stale or Awaiting User Response labels, then those labels will be removed

name: 🎯 Remove Pending Labels on Close
on:
  issues:
    types: [closed]
jobs:
  remove-labels:
   runs-on: ubuntu-latest
   steps:
   - name: Remove Labels when Closed
     uses: actions-cool/issues-helper@v2
     with:
      actions: remove-labels
      token: ${{ secrets.BOT_GITHUB_TOKEN || secrets.GITHUB_TOKEN }}
      issue-number: ${{ github.event.issue.number }}
      labels: '🚏 Awaiting User Response,⚰️ Stale,👤 Awaiting Maintainer Response'