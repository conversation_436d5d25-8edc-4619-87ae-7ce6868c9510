# When a new comment is added to an issue, if it had the Stale or Awaiting User Response labels, then those labels will be removed

name: 🎯 Add/ Remove Awaiting Response Labels
on:
  issue_comment:
    types: [created]
jobs:
  remove-stale:
   runs-on: ubuntu-latest
   if: ${{ github.event.comment.author_association != 'COLLABORATOR' && github.event.comment.author_association != 'OWNER' }}
   steps:
   - name: Remove Stale labels when Updated
     uses: actions-cool/issues-helper@v2
     with:
      actions: remove-labels
      token: ${{ secrets.BOT_GITHUB_TOKEN || secrets.GITHUB_TOKEN }}
      issue-number: ${{ github.event.issue.number }}
      labels: '🚏 Awaiting User Response,⚰️ Stale'

  add-awaiting-author:
   runs-on: ubuntu-latest
   if: ${{!github.event.issue.pull_request && github.event.comment.author_association != 'COLLABORATOR' && github.event.comment.author_association != 'OWNER' && github.event.issue.state == 'open' }}
   steps:
   - name: Add Awaiting Author labels when Updated
     uses: actions-cool/issues-helper@v2
     with:
      actions: add-labels
      token: ${{ secrets.BOT_GITHUB_TOKEN || secrets.GITHUB_TOKEN }}
      issue-number: ${{ github.event.issue.number }}
      labels: '👤 Awaiting Maintainer Response'

  remove-awaiting-author:
   runs-on: ubuntu-latest
   if: ${{ github.event.comment.author_association == 'OWNER' }}
   steps:
   - name: Remove Awaiting Author labels when Updated
     uses: actions-cool/issues-helper@v2
     with:
      actions: remove-labels
      token: ${{ secrets.BOT_GITHUB_TOKEN || secrets.GITHUB_TOKEN }}
      issue-number: ${{ github.event.issue.number }}
      labels: '👤 Awaiting Maintainer Response'