{"input_sequence": "<|user|>", "output_sequence": "<|model|>", "last_output_sequence": "", "system_sequence": "", "stop_sequence": "<|user|>", "wrap": false, "macro": true, "names_behavior": "always", "activation_regex": "", "system_sequence_prefix": "<|system|>", "system_sequence_suffix": "", "first_output_sequence": "", "skip_examples": false, "output_suffix": "", "input_suffix": "", "system_suffix": "", "user_alignment_message": "", "system_same_as_user": true, "last_system_sequence": "", "name": "<PERSON><PERSON><PERSON><PERSON>"}