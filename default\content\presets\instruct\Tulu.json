{"input_sequence": "<|user|>\n", "output_sequence": "<|assistant|>\n", "first_output_sequence": "", "last_output_sequence": "", "system_sequence_prefix": "", "system_sequence_suffix": "", "stop_sequence": "<|end_of_text|>", "wrap": false, "macro": true, "names_behavior": "always", "activation_regex": "", "skip_examples": false, "output_suffix": "<|end_of_text|>\n", "input_suffix": "\n", "system_sequence": "<|system|>\n", "system_suffix": "\n", "user_alignment_message": "", "last_system_sequence": "", "system_same_as_user": false, "name": "<PERSON><PERSON>"}