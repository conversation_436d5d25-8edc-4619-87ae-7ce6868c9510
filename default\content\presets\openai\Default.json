{"chat_completion_source": "openai", "openai_model": "gpt-4-turbo", "claude_model": "claude-3-5-sonnet-20240620", "windowai_model": "", "openrouter_model": "OR_Website", "openrouter_use_fallback": false, "openrouter_group_models": false, "openrouter_sort_models": "alphabetically", "ai21_model": "jamba-1.5-large", "mistralai_model": "mistral-large-latest", "custom_model": "", "custom_url": "", "custom_include_body": "", "custom_exclude_body": "", "custom_include_headers": "", "google_model": "gemini-pro", "temperature": 1, "frequency_penalty": 0, "presence_penalty": 0, "top_p": 1, "top_k": 0, "top_a": 0, "min_p": 0, "repetition_penalty": 1, "openai_max_context": 4095, "openai_max_tokens": 300, "wrap_in_quotes": false, "names_behavior": 0, "send_if_empty": "", "jailbreak_system": false, "impersonation_prompt": "[Write your next reply from the point of view of {{user}}, using the chat history so far as a guideline for the writing style of {{user}}. Don't write as {{char}} or system. Don't describe actions of {{char}}.]", "new_chat_prompt": "[Start a new Chat]", "new_group_chat_prompt": "[Start a new group chat. Group members: {{group}}]", "new_example_chat_prompt": "[Example Chat]", "continue_nudge_prompt": "[Continue the following message. Do not include ANY parts of the original message. Use capitalization and punctuation as if your reply is a part of the original message: {{lastChatMessage}}]", "bias_preset_selected": "<PERSON><PERSON><PERSON> (none)", "reverse_proxy": "", "proxy_password": "", "max_context_unlocked": false, "wi_format": "{0}", "scenario_format": "{{scenario}}", "personality_format": "{{personality}}", "group_nudge_prompt": "[Write the next reply only as {{char}}.]", "stream_openai": true, "prompts": [{"name": "Main Prompt", "system_prompt": true, "role": "system", "content": "Write {{char}}'s next reply in a fictional chat between {{char}} and {{user}}.", "identifier": "main"}, {"name": "Auxiliary Prompt", "system_prompt": true, "role": "system", "content": "", "identifier": "nsfw"}, {"identifier": "dialogue<PERSON><PERSON><PERSON>s", "name": "Chat Examples", "system_prompt": true, "marker": true}, {"name": "Post-History Instructions", "system_prompt": true, "role": "system", "content": "", "identifier": "jailbreak"}, {"identifier": "chatHistory", "name": "Chat History", "system_prompt": true, "marker": true}, {"identifier": "worldInfoAfter", "name": "World Info (after)", "system_prompt": true, "marker": true}, {"identifier": "worldInfoBefore", "name": "World Info (before)", "system_prompt": true, "marker": true}, {"identifier": "enhanceDefinitions", "role": "system", "name": "Enhance Definitions", "content": "If you have more knowledge of {{char}}, add to the character's lore and personality to enhance them but keep the Character Sheet's definitions absolute.", "system_prompt": true, "marker": false}, {"identifier": "charDescription", "name": "Char Description", "system_prompt": true, "marker": true}, {"identifier": "charPersonality", "name": "Char Personality", "system_prompt": true, "marker": true}, {"identifier": "scenario", "name": "<PERSON><PERSON><PERSON>", "system_prompt": true, "marker": true}, {"identifier": "personaDescription", "name": "Persona Description", "system_prompt": true, "marker": true}], "prompt_order": [{"character_id": 100000, "order": [{"identifier": "main", "enabled": true}, {"identifier": "worldInfoBefore", "enabled": true}, {"identifier": "charDescription", "enabled": true}, {"identifier": "charPersonality", "enabled": true}, {"identifier": "scenario", "enabled": true}, {"identifier": "enhanceDefinitions", "enabled": false}, {"identifier": "nsfw", "enabled": true}, {"identifier": "worldInfoAfter", "enabled": true}, {"identifier": "dialogue<PERSON><PERSON><PERSON>s", "enabled": true}, {"identifier": "chatHistory", "enabled": true}, {"identifier": "jailbreak", "enabled": true}]}, {"character_id": 100001, "order": [{"identifier": "main", "enabled": true}, {"identifier": "worldInfoBefore", "enabled": true}, {"identifier": "personaDescription", "enabled": true}, {"identifier": "charDescription", "enabled": true}, {"identifier": "charPersonality", "enabled": true}, {"identifier": "scenario", "enabled": true}, {"identifier": "enhanceDefinitions", "enabled": false}, {"identifier": "nsfw", "enabled": true}, {"identifier": "worldInfoAfter", "enabled": true}, {"identifier": "dialogue<PERSON><PERSON><PERSON>s", "enabled": true}, {"identifier": "chatHistory", "enabled": true}, {"identifier": "jailbreak", "enabled": true}]}], "api_url_scale": "", "show_external_models": false, "assistant_prefill": "", "assistant_impersonation": "", "claude_use_sysprompt": false, "use_alt_scale": false, "squash_system_messages": false, "image_inlining": false, "bypass_status_check": false, "continue_prefill": false, "continue_postfix": " ", "seed": -1, "n": 1}