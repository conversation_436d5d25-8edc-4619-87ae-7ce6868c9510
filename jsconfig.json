{"compilerOptions": {"module": "ESNext", "target": "ES2023", "moduleResolution": "Node", "strictNullChecks": true, "strictFunctionTypes": true, "checkJs": true, "allowUmdGlobalAccess": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "strictBindCallApply": true}, "exclude": ["**/node_modules/**", "**/dist/**", "**/.git/**", "public/lib/**", "backups/**", "data/**", "cache/**", "src/tokenizers/**", "docker/**"]}