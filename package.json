{"dependencies": {"@adobe/css-tools": "^4.4.0", "@agnai/sentencepiece-js": "^1.1.1", "@agnai/web-tokenizers": "^0.1.3", "@iconfu/svg-inject": "^1.2.3", "@mozilla/readability": "^0.5.0", "@popperjs/core": "^2.11.8", "@zeldafan0225/ai_horde": "^5.1.0", "archiver": "^7.0.1", "bing-translate-api": "^4.0.2", "body-parser": "^1.20.2", "bowser": "^2.11.0", "command-exists": "^1.2.9", "compression": "^1", "cookie-parser": "^1.4.6", "cookie-session": "^2.1.0", "cors": "^2.8.5", "csrf-csrf": "^2.2.3", "diff-match-patch": "^1.0.5", "dompurify": "^3.1.7", "droll": "^0.2.1", "express": "^4.21.0", "form-data": "^4.0.0", "fuse.js": "^7.0.0", "google-translate-api-browser": "^3.0.1", "google-translate-api-x": "^10.7.1", "handlebars": "^4.7.8", "helmet": "^7.1.0", "highlight.js": "^11.10.0", "html-entities": "^2.5.2", "iconv-lite": "^0.6.3", "ip-matching": "^2.1.2", "ipaddr.js": "^2.0.1", "jimp": "^0.22.10", "localforage": "^1.10.0", "lodash": "^4.17.21", "mime-types": "^2.1.35", "moment": "^2.30.1", "morphdom": "^2.7.4", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "node-persist": "^4.0.1", "open": "^8.4.2", "png-chunk-text": "^1.0.0", "png-chunks-encode": "^1.0.0", "png-chunks-extract": "^1.0.0", "proxy-agent": "^6.4.0", "rate-limiter-flexible": "^5.0.0", "response-time": "^2.3.2", "sanitize-filename": "^1.6.3", "seedrandom": "^3.0.5", "showdown": "^2.1.0", "sillytavern-transformers": "2.14.6", "simple-git": "^3.19.1", "slidetoggle": "^4.0.0", "tiktoken": "^1.0.16", "url-join": "^5.0.0", "vectra": "^0.2.2", "wavefile": "^11.0.0", "webpack": "^5.95.0", "write-file-atomic": "^5.0.1", "ws": "^8.17.1", "yaml": "^2.3.4", "yargs": "^17.7.1", "yauzl": "^2.10.0"}, "engines": {"node": ">= 18"}, "overrides": {"vectra": {"openai": "^4.17.0"}, "axios": {"follow-redirects": "^1.15.4"}, "node-fetch": {"whatwg-url": "^14.0.0"}}, "name": "silly<PERSON>vern", "type": "module", "license": "AGPL-3.0", "repository": {"type": "git", "url": "https://github.com/SillyTavern/SillyTavern.git"}, "version": "1.12.11", "scripts": {"start": "node server.js", "start:deno": "deno run --allow-run --allow-net --allow-read --allow-write --allow-sys --allow-env server.js", "start:bun": "bun server.js", "start:no-csrf": "node server.js --disableCsrf", "postinstall": "node post-install.js", "lint": "eslint \"src/**/*.js\" \"public/**/*.js\" ./*.js", "lint:fix": "eslint \"src/**/*.js\" \"public/**/*.js\" ./*.js --fix", "plugins:update": "node plugins update", "plugins:install": "node plugins install"}, "bin": {"sillytavern": "./server.js"}, "rules": {"no-path-concat": "off", "no-var": "off"}, "main": "server.js", "devDependencies": {"@types/archiver": "^6.0.2", "@types/command-exists": "^1.2.3", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.7", "@types/cookie-session": "^2.0.49", "@types/cors": "^2.8.17", "@types/deno": "^2.0.0", "@types/express": "^4.17.21", "@types/jquery": "^3.5.29", "@types/jquery-cropper": "^1.0.4", "@types/jquery.transit": "^0.9.33", "@types/jqueryui": "^1.12.23", "@types/lodash": "^4.17.10", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.12", "@types/node": "^18.19.55", "@types/node-persist": "^3.1.8", "@types/png-chunk-text": "^1.0.3", "@types/png-chunks-encode": "^1.0.2", "@types/png-chunks-extract": "^1.0.2", "@types/response-time": "^2.3.8", "@types/select2": "^4.0.63", "@types/toastr": "^2.1.43", "@types/write-file-atomic": "^4.0.3", "@types/yargs": "^17.0.33", "@types/yauzl": "^2.10.3", "eslint": "^8.57.0"}}