{"Favorite": "<PERSON><PERSON><PERSON>", "Tag": "<PERSON><PERSON><PERSON><PERSON>", "Duplicate": "<PERSON><PERSON><PERSON><PERSON>", "Persona": "<PERSON>a", "Delete": "Elimina", "AI Response Configuration": "Configurazione Risposta AI", "AI Configuration panel will stay open": "Il pannello di configurazione dell'AI rimarrà aperto", "clickslidertips": "Fare clic per inserire manualmente i valori.", "MAD LAB MODE ON": "MODALITÀ MAD LAB ATTIVA", "Documentation on sampling parameters": "Documentazione sui parametri di campionamento", "kobldpresets": "Preimpostazioni Kobold", "guikoboldaisettings": "Impostazioni dell'interfaccia KoboldAI", "Update current preset": "Aggiorna l'attuale preset", "Save preset as": "Salva preimpostazione come", "Import preset": "Importa preset", "Export preset": "Esporta preset", "Restore current preset": "Ripristina l'attuale preimpostazione", "Delete the preset": "Elimina il preset", "novelaipresets": "Preimpostazioni NovelAI", "Default": "Predefinito", "openaipresets": "Preimpostazioni OpenAI", "Text Completion presets": "Preimpostazioni completamento del testo", "AI Module": "Modulo AI", "Changes the style of the generated text.": "Cambia lo stile del testo generato.", "No Module": "Nessun modulo", "Instruct": "<PERSON><PERSON><PERSON><PERSON>", "Prose Augmenter": "Aumentatore di prosa", "Text Adventure": "Avventura testuale", "response legth(tokens)": "Lunghezza della risposta (token)", "Streaming": "Streaming", "Streaming_desc": "Mostra la risposta pezzo per pezzo man mano che viene generata", "context size(tokens)": "Dimensione del contesto (token)", "unlocked": "Sbloccato", "Only enable this if your model supports context sizes greater than 8192 tokens": "Abilita solo se il tuo modello supporta dimensioni del contesto superiori a 8192 token", "Max prompt cost:": "Costo massimo immediato:", "Display the response bit by bit as it is generated.": "Visualizza la risposta pezzo per pezzo mentre viene generata.", "When this is off, responses will be displayed all at once when they are complete.": "Quando questo è disattivato, le risposte verranno visualizzate tutte in una volta quando sono complete.", "Temperature": "Temperatura", "rep.pen": "Pena per ripetizione", "Rep. Pen. Range.": "Intervallo di Pen. Rip.", "Rep. Pen. Slope": "Pendenza della penalità di ripetizione", "Rep. Pen. Freq.": "Freq. Pen. Rip.", "Rep. Pen. Presence": "Presenza Pen. Rip.", "TFS": "TFS", "Phrase Repetition Penalty": "Penalità per la ripetizione di frasi", "Off": "Spent<PERSON>", "Very light": "<PERSON><PERSON><PERSON> leggero", "Light": "<PERSON><PERSON><PERSON>", "Medium": "Medio", "Aggressive": "Aggressivo", "Very aggressive": "Molto aggressivo", "Unlocked Context Size": "Dimensione contesto sbloccato", "Unrestricted maximum value for the context slider": "Valore massimo illimitato per lo slider di contesto", "Context Size (tokens)": "Dimensione del contesto (token)", "Max Response Length (tokens)": "Lunghezza massima della risposta (token)", "Multiple swipes per generation": "<PERSON>ù passaggi per generazione", "Enable OpenAI completion streaming": "Abilita lo streaming di completamento OpenAI", "Frequency Penalty": "Penalità di frequenza", "Presence Penalty": "Penalità di presenza", "Count Penalty": "<PERSON><PERSON>", "Top K": "Top K", "Top P": "Top P", "Repetition Penalty": "Penalità per Ripetizione", "Min P": "<PERSON>", "Top A": "Top A", "Quick Prompts Edit": "Modifica rapida delle richieste", "Main": "<PERSON><PERSON>", "NSFW": "NSFW", "Jailbreak": "Sblocco", "Utility Prompts": "Richieste di utilità", "Impersonation prompt": "Prompt di impersonazione", "Restore default prompt": "Rip<PERSON>ina il prompt predefinito", "Prompt that is used for Impersonation function": "Prompt che viene utilizzato per la funzione di impersonazione", "World Info Format Template": "Modello di formato informazioni sul mondo", "Restore default format": "Ripristina il formato predefinito", "Wraps activated World Info entries before inserting into the prompt.": "Inserisce le voci attivate delle Informazioni mondiali prima di inserirle nel prompt.", "scenario_format_template_part_1": "<PERSON><PERSON><PERSON><PERSON>", "scenario_format_template_part_2": "per contrassegnare un punto in cui è inserito il contenuto.", "Scenario Format Template": "Modello di formato dello scenario", "Personality Format Template": "Modello di formato della personalità", "Group Nudge Prompt Template": "Modello di richiesta di spinta del gruppo", "Sent at the end of the group chat history to force reply from a specific character.": "Inviato alla fine della cronologia della chat di gruppo per forzare la risposta di un personaggio specifico.", "New Chat": "Nuova chiacchierata", "Restore new chat prompt": "Ripristina il nuovo messaggio di chat", "Set at the beginning of the chat history to indicate that a new chat is about to start.": "Impostato all'inizio della cronologia della chat per indicare che sta per iniziare una nuova chat.", "New Group Chat": "Nuova chat di gruppo", "Restore new group chat prompt": "Rip<PERSON>ina il prompt predefinito", "Set at the beginning of the chat history to indicate that a new group chat is about to start.": "Impostato all'inizio della cronologia della chat per indicare che sta per iniziare una nuova chat di gruppo.", "New Example Chat": "Nuova chat di esempio", "Set at the beginning of Dialogue examples to indicate that a new example chat is about to start.": "Impostato all'inizio degli esempi di dialogo per indicare che sta per iniziare una nuova chat di esempio.", "Continue nudge": "Continua a spingere", "Set at the end of the chat history when the continue button is pressed.": "Impostato alla fine della cronologia della chat quando si preme il pulsante Continua.", "Replace empty message": "Sostituisci messaggio vuoto", "Send this text instead of nothing when the text box is empty.": "Invia questo testo invece di niente quando la casella di testo è vuota.", "Seed": "<PERSON><PERSON>", "Set to get deterministic results. Use -1 for random seed.": "Impostato per ottenere risultati deterministici. Usa -1 per il seme casuale.", "Temperature controls the randomness in token selection": "La temperatura controlla la casualità nella selezione dei token", "Top_K_desc": "Top K imposta una quantità massima di token migliori che possono essere scelti", "Top_P_desc": "Top P (alias campionamento del nucleo)", "Typical P": "P tipico", "Typical_P_desc": "Il campionamento P tipico prioritizza i token in base alla loro deviazione dall'entropia media del set", "Min_P_desc": "Min P imposta una probabilità minima di base", "Top_A_desc": "Top A imposta una soglia per la selezione dei token in base al quadrato della probabilità più alta del token", "Tail_Free_Sampling_desc": "Campionamento senza coda (TFS)", "rep.pen range": "Intervallo di pena per ripetizione", "Mirostat": "Mirostat", "Mode": "Modalità", "Mirostat_Mode_desc": "Un valore pari a 0 disabilita completamente Mirostat. 1 è per Mirostat 1.0 e 2 è per Mirostat 2.0", "Tau": "Tau", "Mirostat_Tau_desc": "Controlla la variabilità delle uscite Mirostat", "Eta": "Eta", "Mirostat_Eta_desc": "Controlla il tasso di apprendimento di Mirostat", "Ban EOS Token": "Bandisci il token EOS", "Ban_EOS_Token_desc": "Vietare il token End-of-Sequence (EOS) con KoboldCpp (e possibilmente anche altri token con KoboldAI). Ottimo per la scrittura di storie, ma non dovrebbe essere utilizzato per la chat e la modalità istruzioni.", "GBNF Grammar": "Grammatica GBNF", "Type in the desired custom grammar": "Digita la grammatica personalizzata desiderata", "Samplers Order": "Ordine dei Campionatori", "Samplers will be applied in a top-down order. Use with caution.": "I Campionatori saranno applicati in ordine dall'alto verso il basso. Usare con cautela.", "Tail Free Sampling": "Campionamento senza coda", "Load koboldcpp order": "Carica l'ordine koboldcpp", "Preamble": "Preambolo", "Use style tags to modify the writing style of the output.": "Usa i tag di stile per modificare lo stile di scrittura dell'output.", "Banned Tokens": "Token banditi", "Sequences you don't want to appear in the output. One per line.": "Sequenze che non vuoi che compaiano nell'output. Una per riga.", "Logit Bias": "Bias del logit", "Add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Helps to ban or reenforce the usage of certain words": "Aiuta a vietare o rafforzare l'uso di determinate parole", "CFG Scale": "Scala CFG", "Negative Prompt": "Prompt negativo", "Add text here that would make the AI generate things you don't want in your outputs.": "Aggiungi qui del testo che farebbe generare all'IA cose che non vuoi nei tuoi output.", "Used if CFG Scale is unset globally, per chat or character": "Usato se la scala CFG non è impostata globalmente, per chat o carattere", "Mirostat Tau": "Tau di Mirostat", "Mirostat LR": "Mirostat LR", "Min Length": "Lunghezza minima", "Top K Sampling": "Campionamento top K", "Nucleus Sampling": "Campionamento nucleare", "Top A Sampling": "Campionamento top A", "CFG": "CFG", "Neutralize Samplers": "Neutralizza i campionatori", "Set all samplers to their neutral/disabled state.": "Imposta tutti i campionatori sullo stato neutro/disabilitato.", "Sampler Select": "Seleziona campionatore", "Customize displayed samplers or add custom samplers.": "Personalizza i campionatori visualizzati o aggiungi campionatori personalizzati.", "Epsilon Cutoff": "Taglio epsilon", "Epsilon cutoff sets a probability floor below which tokens are excluded from being sampled": "Il taglio epsilon imposta un limite di probabilità al di sotto del quale i token vengono esclusi dal campionamento", "Eta Cutoff": "Taglio eta", "Eta_Cutoff_desc": "Il taglio Eta è il parametro principale della tecnica di campionamento Eta speciale.&#13;In unità di 1e-4; un valore ragionevole è 3.&#13;Impostare su 0 per disabilitare.&#13;Consultare l'articolo Truncation Sampling as Language Model Desmoothing di <PERSON> et al. (2022) per i dettagli.", "rep.pen decay": "Decadimento della penna rappresentante", "Encoder Rep. Pen.": "Penalità di ripetizione dell'encoder", "No Repeat Ngram Size": "Dimensione ngram senza ripetizione", "Skew": "Storto", "Max Tokens Second": "<PERSON> per secondo", "Smooth Sampling": "Campionamento regolare", "Smooth_Sampling_desc": "Consente di utilizzare trasformazioni quadratiche/cubiche per regolare la distribuzione. Valori inferiori del fattore di livellamento saranno più creativi, solitamente tra 0,2 e 0,3 è il punto debole (assumendo che la curva = 1). Valori più elevati della curva di livellamento renderanno la curva più ripida, penalizzando in modo più aggressivo le scelte a bassa probabilità. La curva 1.0 equivale a utilizzare solo il fattore di livellamento.", "Smoothing Factor": "Fattore di smorzamento", "Smoothing Curve": "Curva di livellamento", "DRY_Repetition_Penalty_desc": "DRY penalizza i token che estenderebbero la fine dell'input in una sequenza precedentemente verificata nell'input. Imposta il moltiplicatore su 0 per disabilitarlo.", "DRY Repetition Penalty": "Penalità di ripetizione ASCIUTTA", "DRY_Multiplier_desc": "Impostare su un valore > 0 per abilitare DRY. Controlla l'entità della penalità per le sequenze penalizzate più brevi.", "Multiplier": "Moltiplicatore", "DRY_Base_desc": "Controlla la velocità con cui la penalità aumenta con l'aumentare della lunghezza della sequenza.", "Base": "Base", "DRY_Allowed_Length_desc": "Sequenza più lunga che può essere ripetuta senza essere penalizzata.", "Allowed Length": "<PERSON>ng<PERSON><PERSON>", "Penalty Range": "Gamma di penalità", "DRY_Sequence_Breakers_desc": "Token attraverso i quali la corrispondenza della sequenza non viene continuata. Specificato come elenco separato da virgole di stringhe tra virgolette.", "Sequence Breakers": "Interruttori di sequenza", "JSON-serialized array of strings.": "Matrice di stringhe serializzate JSON.", "Dynamic Temperature": "Temperatura dinamica", "Scale Temperature dynamically per token, based on the variation of probabilities": "Scala la temperatura dinamicamente per token, in base alla variazione delle probabilità", "Minimum Temp": "Temperatura minima", "Maximum Temp": "Temperatura massima", "Exponent": "Esponente", "Mirostat (mode=1 is only for llama.cpp)": "Mirostat (la modalità=1 è solo per llama.cpp)", "Mirostat_desc": "Mirostat è un termostato per la perplessità dell'output", "Mirostat Mode": "Modalità Mirostat", "Variability parameter for Mirostat outputs": "Parametro di variabilità per le uscite di Mirostat", "Mirostat Eta": "Eta di Mirostat", "Learning rate of Mirostat": "Tasso di apprendimento di Mirostat", "Beam search": "Ricerca a fascio", "Helpful tip coming soon.": "Suggerimento utile in arrivo.", "Number of Beams": "Numero di fasci", "Length Penalty": "Penalità di lunghezza", "Early Stopping": "Arresto anticipato", "Contrastive search": "Ricerca contrastiva", "Penalty Alpha": "Alfa di penalità", "Strength of the Contrastive Search regularization term. Set to 0 to disable CS": "Intensità del termine di regolarizzazione della ricerca contrastiva. Impostare su 0 per disabilitare CS.", "Do Sample": "Eseguire il campionamento", "Add BOS Token": "Aggiungi token BOS", "Add the bos_token to the beginning of prompts. Disabling this can make the replies more creative": "Aggiungi il bos_token all'inizio dei suggerimenti. Disabilitare questa opzione può rendere le risposte più creative", "Ban the eos_token. This forces the model to never end the generation prematurely": "Bandisci il token eos. <PERSON>o costringe il modello a non terminare mai la generazione prematuramente", "Ignore EOS Token": "Ignora il token EOS", "Ignore the EOS Token even if it generates.": "Ignora il token EOS anche se viene generato.", "Skip Special Tokens": "Salta i token speciali", "Temperature Last": "Ultima temperatura", "Temperature_Last_desc": "Usa l'ultimo campionatore di temperatura", "Speculative Ngram": "Ngram speculativo", "Use a different speculative decoding method without a draft model": "Utilizzare un diverso metodo di decodifica speculativa senza una bozza di modello.\rÈ preferibile l'utilizzo di un modello in bozza. Il ngram speculativo non è altrettanto efficace.", "Spaces Between Special Tokens": "Spazi tra i gettoni speciali", "LLaMA / Mistral / Yi models only": "Solo modelli LLaMA / Mistral / Yi", "Example: some text [42, 69, 1337]": "Esempio: un po' di testo [42, 69, 1337]", "Classifier Free Guidance. More helpful tip coming soon": "Guida gratuita del classificatore. Presto arriverà un consiglio più utile", "Scale": "Scala", "JSON Schema": "Schema JSON", "Type in the desired JSON schema": "Digita lo schema JSON desiderato", "Grammar String": "Stringa grammaticale", "GBNF or EBNF, depends on the backend in use. If you're using this you should know which.": "GBNF o EBNF, dipende dal backend in uso. Se stai usando questo dovresti sapere quale.", "Top P & Min P": "P massimo e P minimo", "Load default order": "Carica ordine predefinito", "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.": "Solo lama.cpp. Determina l'ordine dei campionatori. Se la modalità Mirostat non è 0, l'ordine del campionatore viene ignorato.", "Sampler Priority": "Priorità del campionatore", "Ooba only. Determines the order of samplers.": "Solo Ooba. Determina l'ordine dei campionatori.", "Character Names Behavior": "Comportamento dei nomi dei personaggi", "Helps the model to associate messages with characters.": "Aiuta il modello ad associare i messaggi ai personaggi.", "None": "<PERSON><PERSON><PERSON>", "character_names_default": "Fatta eccezione per i gruppi e i personaggi passati. Altrimenti, assicurati di fornire i nomi nel prompt.", "Don't add character names.": "Non aggiungere nomi di personaggi.", "Completion": "Oggetto di completamento", "character_names_completion": "Si applicano restrizioni: solo caratteri alfanumerici latini e trattini bassi. Non funziona con tutte le fonti, in particolare: Claude, MistralAI, Google.", "Add character names to completion objects.": "Aggiungere nomi di personaggi agli oggetti di completamento.", "Message Content": "Contenuto del messaggio", "Prepend character names to message contents.": "Anteponi i nomi dei caratteri al contenuto del messaggio.", "Continue Postfix": "<PERSON><PERSON><PERSON>", "The next chunk of the continued message will be appended using this as a separator.": "La parte successiva del messaggio continuato verrà aggiunta utilizzando questo come separatore.", "Space": "Spazio", "Newline": "Nuova linea", "Double Newline": "<PERSON>ppia nuova riga", "Wrap user messages in quotes before sending": "Avvolgi i messaggi degli utenti tra virgolette prima di inviarli", "Wrap in Quotes": "Avvolgi tra virgolette", "Wrap entire user message in quotes before sending.": "Avvolgi l'intero messaggio dell'utente tra virgolette prima di inviarlo.", "Leave off if you use quotes manually for speech.": "<PERSON><PERSON> perdere se usi manualmente le virgolette per il discorso.", "Continue prefill": "Continua il precompilamento", "Continue sends the last message as assistant role instead of system message with instruction.": "Continua invia l'ultimo messaggio come ruolo assistente invece di messaggio di sistema con istruzioni.", "Squash system messages": "Sistema mess<PERSON> s<PERSON>", "Combines consecutive system messages into one (excluding example dialogues). May improve coherence for some models.": "Combina i messaggi di sistema consecutivi in uno solo (escludendo i dialoghi di esempio). Potrebbe migliorare la coerenza per alcuni modelli.", "Enable function calling": "Abilita la chiamata alla funzione", "Send inline images": "Invia immagini inline", "image_inlining_hint_1": "Invia immagini nei prompt se il modello lo supporta (ad esempio GPT-4V, Claude 3 o Llava 13B).\n                                                Usa il", "image_inlining_hint_2": "azione su qualsiasi messaggio o il", "image_inlining_hint_3": "menu per allegare un file immagine alla chat.", "Inline Image Quality": "Qualità dell'immagine in linea", "openai_inline_image_quality_auto": "Auto", "openai_inline_image_quality_low": "<PERSON><PERSON>", "openai_inline_image_quality_high": "Alto", "Use AI21 Tokenizer": "Usa il token AI21", "Use the appropriate tokenizer for Jurassic models, which is more efficient than GPT's.": "Utilizza il tokenizzatore appropriato per i modelli giurassici, che è più efficiente di quello GPT.", "Use Google Tokenizer": "Usa il Tokenizer di Google", "Use the appropriate tokenizer for Google models via their API. Slower prompt processing, but offers much more accurate token counting.": "Utilizza il tokenizer appropriato per i modelli Google tramite la loro API. Elaborazione dei prompt più lenta, ma offre un conteggio dei token molto più accurato.", "Use system prompt": "Utilizza il prompt del sistema", "(Gemini 1.5 Pro/Flash only)": "(Solo Gemini 1.5 Pro/Flash)", "Merges_all_system_messages_desc_1": "Unisce tutti i messaggi di sistema fino al primo messaggio con un ruolo non di sistema e li invia in un file", "Merges_all_system_messages_desc_2": "campo.", "Assistant Prefill": "Prefill assistente", "Start Claude's answer with...": "Inizia la risposta di Claude con...", "Assistant Impersonation Prefill": "Precompilazione imitazione assistente", "Use system prompt (Claude 2.1+ only)": "<PERSON>a prompt di sistema (solo <PERSON> 2.1+)", "Send the system prompt for supported models. If disabled, the user message is added to the beginning of the prompt.": "Invia il prompt di sistema per i modelli supportati. Se disabilitato, il messaggio dell'utente viene aggiunto all'inizio del prompt.", "User first message": "Primo messaggio dell'utente", "Restore User first message": "Ripristina il primo messaggio dell'utente", "Human message": "<PERSON><PERSON><PERSON> um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ecc.\nNon aggiunge nulla quando è vuoto, ovvero richiede un nuovo prompt con il ruolo \"utente\".", "New preset": "Nuovo preset", "Delete preset": "Elimina preset", "View / Edit bias preset": "Visualizza / Modifica la preimpostazione del bias", "Add bias entry": "Aggiungi voce di bias", "Most tokens have a leading space.": "La maggior parte dei gettoni ha uno spazio iniziale.", "API Connections": "Connessioni API", "Text Completion": "Completamento del testo", "Chat Completion": "Completamento della chat", "NovelAI": "NovelAI", "AI Horde": "AI Horde", "KoboldAI": "KoboldAI", "Avoid sending sensitive information to the Horde.": "Evita di inviare informazioni sensibili all'Orda.", "Review the Privacy statement": "Revisione della dichiarazione sulla privacy", "Register a Horde account for faster queue times": "Registrati per un account Horde per tempi di attesa più brevi", "Learn how to contribute your idle GPU cycles to the Horde": "Sc<PERSON><PERSON> come contribuire ai cicli GPU inattivi all'Orda", "Adjust context size to worker capabilities": "Regola la dimensione del contesto in base alle capacità del lavoratore", "Adjust response length to worker capabilities": "Regola la lunghezza della risposta in base alle capacità del lavoratore", "Can help with bad responses by queueing only the approved workers. May slowdown the response time.": "<PERSON><PERSON>ò aiutare con le risposte negative mettendo in coda solo i lavoratori approvati. Potrebbe rallentare il tempo di risposta.", "Trusted workers only": "Solo lavoratori fidati", "API key": "Chiave API", "Get it here:": "<PERSON><PERSON><PERSON><PERSON> qui:", "Register": "Registrati", "View my Kudos": "Visualizza i miei <PERSON>", "Enter": "<PERSON><PERSON><PERSON><PERSON>", "to use anonymous mode.": "per utilizzare la modalità anonima.", "Clear your API key": "Cancella la tua chiave API", "For privacy reasons, your API key will be hidden after you reload the page.": "Per motivi di privacy, la tua chiave API sarà nascosta dopo che ricarichi la pagina.", "Models": "<PERSON><PERSON>", "Refresh models": "Aggiorna modelli", "-- Horde models not loaded --": "-- <PERSON><PERSON> Orda non caricati --", "Not connected...": "Non connesso...", "API url": "URL API", "Example: http://127.0.0.1:5000/api ": "Esempio: http://127.0.0.1:5000/api", "Connect": "Collega", "Cancel": "<PERSON><PERSON><PERSON>", "Novel API key": "Chiave API di Novel", "Get your NovelAI API Key": "Ottieni la tua Chiave API NovelAI", "Enter it in the box below": "Inser<PERSON><PERSON><PERSON> nella casella qui sotto", "Novel AI Model": "Modello Novel AI", "No connection...": "Nessuna connessione...", "API Type": "Tipo di API", "Default (completions compatible)": "Predefinito [Compatibile con OpenAI/completamenti: oobabooga, LM Studio, ecc.]", "TogetherAI API Key": "Chiave API di TogetherAI", "TogetherAI Model": "Modello TogetherAI", "-- Connect to the API --": "-- Collegati all'API --", "OpenRouter API Key": "Chiave API di OpenRouter", "Click Authorize below or get the key from": "Fai clic su Autorizza qui sotto o ottieni la chiave da", "View Remaining Credits": "Visualizza i crediti rimanenti", "OpenRouter Model": "<PERSON><PERSON> OpenRouter", "Model Providers": "Fornitori di modelli", "InfermaticAI API Key": "Chiave API InfermaticAI", "InfermaticAI Model": "Modello InfermaticAI", "DreamGen API key": "Chiave API DreamGen", "DreamGen Model": "Modello DreamGen", "Mancer API key": "Chiave API di Mancer", "Mancer Model": "<PERSON><PERSON>", "Make sure you run it with": "Assicurati di eseguirlo con", "flag": "bandiera", "API key (optional)": "Chiave API (opzionale)", "Server url": "URL del server", "Example: 127.0.0.1:5000": "Esempio: 127.0.0.1:5000", "Custom model (optional)": "<PERSON><PERSON> (opzionale)", "vllm-project/vllm": "vllm-project/vllm (modalità wrapper API OpenAI)", "vLLM API key": "Chiave API vLLM", "Example: 127.0.0.1:8000": "Esempio: http://127.0.0.1:8000", "vLLM Model": "Modello vLLM", "PygmalionAI/aphrodite-engine": "PygmalionAI/aphrodite-engine (Modalità wrapper per l'API OpenAI)", "Aphrodite API key": "Chiave API di Aphrodite", "Aphrodite Model": "<PERSON><PERSON> di Afrodite", "ggerganov/llama.cpp": "ggerganov/llama.cpp (Server di output)", "Example: 127.0.0.1:8080": "Esempio: 127.0.0.1:8080", "Example: 127.0.0.1:11434": "Esempio: 127.0.0.1:11434", "Ollama Model": "<PERSON><PERSON>", "Download": "Scarica", "Tabby API key": "Chiave API di Tabby", "koboldcpp API key (optional)": "Chiave API koboldcpp (opzionale)", "Example: 127.0.0.1:5001": "Esempio: 127.0.0.1:5001", "Authorize": "Autorizzare", "Get your OpenRouter API token using OAuth flow. You will be redirected to openrouter.ai": "Ottieni il tuo token API di OpenRouter utilizzando il flusso OAuth. Sarai reindirizzato su openrouter.ai", "Bypass status check": "Ignora controllo stato", "Chat Completion Source": "Fonte di Completamento della Chat", "Reverse Proxy": "Proxy inverso", "Proxy Presets": "Preimpostazioni proxy", "Saved addresses and passwords.": "Indirizzi e password salvati.", "Save Proxy": "Salva proxy", "Delete Proxy": "Elimina proxy", "Proxy Name": "Nome proxy", "This will show up as your saved preset.": "Questo verrà visualizzato come preimpostazione salvata.", "Proxy Server URL": "URL del server proxy", "Alternative server URL (leave empty to use the default value).": "URL del server alternativo (lasciare vuoto per utilizzare il valore predefinito).", "Remove your real OAI API Key from the API panel BEFORE typing anything into this box": "Rimuovi la tua vera chiave API OAI dal pannello API PRIMA di digitare qualcosa in questo campo", "We cannot provide support for problems encountered while using an unofficial OpenAI proxy": "Non possiamo fornire supporto per i problemi riscontrati durante l'utilizzo di un proxy non ufficiale di OpenAI", "Doesn't work? Try adding": "Non funziona? Prova ad aggiungere", "at the end!": "alla fine!", "Proxy Password": "Password proxy", "Will be used as a password for the proxy instead of API key.": "Verrà utilizzato come password per il proxy anziché come chiave API.", "Peek a password": "Dai un'occhiata a una password", "OpenAI API key": "Chiave API di OpenAI", "View API Usage Metrics": "Visualizza le metriche sull'uso dell'API", "Follow": "<PERSON><PERSON><PERSON>", "these directions": "queste istruzioni", "to get your OpenAI API key.": "per ottenere la tua chiave API di OpenAI.", "Use Proxy password field instead. This input will be ignored.": "Utilizza invece il campo \"Password proxy\". Questo input verrà ignorato.", "OpenAI Model": "Modello OpenAI", "Bypass API status check": "Ignora il controllo dello stato dell'API", "Show External models (provided by API)": "Mostra modelli esterni (forniti dall'API)", "Get your key from": "<PERSON><PERSON><PERSON> la tua chiave da", "Anthropic's developer console": "Console dello sviluppatore di Anthropic", "Claude Model": "<PERSON><PERSON>", "Window AI Model": "Modello AI di Window", "Model Order": "Ordinamento dei modelli OpenRouter", "Alphabetically": "In ordine alfabetico", "Price": "Prezzo (il più economico)", "Context Size": "Dimensione del contesto", "Group by vendors": "Raggruppa per fornitori", "Group by vendors Description": "Metti i modelli OpenAI in un gruppo, i modelli antropici in un altro gruppo, ecc. Può essere combinato con l'ordinamento.", "Allow fallback routes": "Consenti percorsi alternativi", "Allow fallback routes Description": "Il modello alternativo viene automaticamente scelto se il modello selezionato non può soddisfare la tua richiesta.", "Scale API Key": "Chiave API di Scale", "Clear your cookie": "Cancella il tuo cookie", "Alt Method": "Metodo alternativo", "AI21 API Key": "Chiave API di AI21", "AI21 Model": "Modello AI21", "Google AI Studio API Key": "Chiave API Google AI Studio", "Google Model": "Modello Google", "MistralAI API Key": "Chiave API MistralAI", "MistralAI Model": "Modello MistralAI", "Groq API Key": "Chiave API Groq", "Groq Model": "<PERSON><PERSON>q", "Perplexity API Key": "Chiave API Perplessità", "Perplexity Model": "Modello di perplessità", "Cohere API Key": "Chiave API Cohere", "Cohere Model": "<PERSON><PERSON>", "Custom Endpoint (Base URL)": "Endpoint personalizzato (URL di base)", "Custom API Key": "Chiave API personalizzata", "Available Models": "Modelli disponibili", "Prompt Post-Processing": "Post-elaborazione rapida", "Applies additional processing to the prompt before sending it to the API.": "Applica un'ulteriore elaborazione al prompt prima di inviarlo all'API.", "Verifies your API connection by sending a short test message. Be aware that you'll be credited for it!": "Verifica la tua connessione API inviando un breve messaggio di prova. Tieni presente che verrai accreditato per questo!", "Test Message": "Messaggio di prova", "Auto-connect to Last Server": "Connetti automaticamente all'ultimo server", "Missing key": "❌ Chiave mancante", "Key saved": "✔️ Chiave salvata", "View hidden API keys": "Visualizza le chiavi API nascoste", "AI Response Formatting": "Formattazione Risposta AI", "Advanced Formatting": "Formattazione avanzata", "Context Template": "Modello di contesto", "Auto-select this preset for Instruct Mode": "Seleziona automaticamente questo preset per la Modalità Istruzioni", "Story String": "Stringa della storia", "Example Separator": "Separatore di esempio", "Chat Start": "Inizio chat", "Add Chat Start and Example Separator to a list of stopping strings.": "Aggiungi Inizio chat e Separatore di esempio a un elenco di stringhe di arresto.", "Use as Stop Strings": "Usa come stringhe di arresto", "context_allow_jailbreak": "Include il jailbreak alla fine del prompt, se definito nella carta personaggio E ''Preferisci Char. Il jailbreak'' è abilitato.\nQUESTO NON È CONSIGLIATO PER I MODELLI DI COMPLETAMENTO DEL TESTO, PUÒ PORTARE A UN RISULTATO CATTIVO.", "Allow Jailbreak": "<PERSON><PERSON><PERSON> jailbreak", "Context Order": "Ordine del contesto", "Summary": "Riepilogo", "Author's Note": "Nota dell'Autore", "Example Dialogues": "Dialoghi di esempio", "Hint": "Suggerimento:", "In-Chat Position not affected": "Gli ordini di riepilogo e nota dell'autore vengono influenzati solo quando non hanno una posizione in chat impostata.", "Instruct Mode": "Modalità di istruzione", "Enabled": "Abilitato", "instruct_bind_to_context": "Se abilitati, i modelli di contesto verranno selezionati automaticamente in base al nome del modello di istruzione selezionato o in base alle preferenze.", "Bind to Context": "Collega al contesto", "Presets": "Preimpostazioni", "Auto-select this preset on API connection": "Seleziona automaticamente questo preset alla connessione API", "Activation Regex": "Regex di attivazione", "Wrap Sequences with Newline": "A<PERSON><PERSON><PERSON><PERSON> le sequenze con una nuova riga", "Replace Macro in Sequences": "Sostituisci Macro in Sequenze", "Skip Example Dialogues Formatting": "Salta formattazione dialoghi di esempio", "Include Names": "<PERSON><PERSON><PERSON> i nomi", "Force for Groups and Personas": "Forza per Gruppi e Personaggi", "System Prompt": "Prompt di sistema", "Instruct Mode Sequences": "Sequenze della modalità di istruzione", "System Prompt Wrapping": "Avvolgimento dei prompt di sistema", "Inserted before a System prompt.": "Inserito prima di un prompt di sistema.", "System Prompt Prefix": "Prefisso prompt di sistema", "Inserted after a System prompt.": "Inserito dopo un prompt di sistema.", "System Prompt Suffix": "Suffisso prompt di sistema", "Chat Messages Wrapping": "Avvolgimento dei messaggi di chat", "Inserted before a User message and as a last prompt line when impersonating.": "Inserito prima di un messaggio utente e come ultima riga di prompt quando si impersona.", "User Message Prefix": "Prefisso messaggio utente", "Inserted after a User message.": "Inserito dopo un messaggio dell'utente.", "User Message Suffix": "Suffisso messaggio utente", "Inserted before an Assistant message and as a last prompt line when generating an AI reply.": "Inserito prima di un messaggio dell'Assistente e come ultima riga di prompt durante la generazione di una risposta AI.", "Assistant Message Prefix": "<PERSON><PERSON><PERSON> messaggio assistente", "Inserted after an Assistant message.": "Inserito dopo un messaggio dell'assistente.", "Assistant Message Suffix": "Suffisso messaggio assistente", "Inserted before a System (added by slash commands or extensions) message.": "Inserito prima di un messaggio di sistema (aggiunto da comandi slash o estensioni).", "System Message Prefix": "Prefisso messaggio di sistema", "Inserted after a System message.": "Inserito dopo un messaggio di sistema.", "System Message Suffix": "Suffisso messaggio di sistema", "If enabled, System Sequences will be the same as User Sequences.": "Se abilitate, le sequenze di sistema saranno le stesse delle sequenze utente.", "System same as User": "Sistema uguale all'utente", "Misc. Sequences": "<PERSON><PERSON><PERSON>", "Inserted before the first Assistant's message.": "Inserito prima del primo messaggio dell'Assistente.", "First Assistant Prefix": "Prefisso del primo assistente", "instruct_last_output_sequence": "Inserito prima dell'ultimo messaggio dell'Assistente o come ultima riga di prompt durante la generazione di una risposta AI (eccetto un ruolo neutrale/di sistema).", "Last Assistant Prefix": "<PERSON><PERSON><PERSON> pre<PERSON>sso <PERSON>", "Will be inserted as a last prompt line when using system/neutral generation.": "Verrà inserito come ultima riga di prompt quando si utilizza la generazione di sistema/neutra.", "System Instruction Prefix": "Prefisso istruzione di sistema", "If a stop sequence is generated, everything past it will be removed from the output (inclusive).": "Se viene generata una sequenza di stop, tutto ciò che segue verrà rimosso dall'output (incluso).", "Stop Sequence": "Sequenza di arresto", "Will be inserted at the start of the chat history if it doesn't start with a User message.": "Verrà inserito all'inizio della cronologia chat se non inizia con un messaggio Utente.", "User Filler Message": "Messaggio di riempimento utente", "Context Formatting": "Formattazione del contesto", "(Saved to Context Template)": "(Salvato nel modello di contesto)", "Always add character's name to prompt": "Aggiungi sempre il nome del personaggio alla prompt", "Generate only one line per request": "Genera solo una riga per richiesta", "Trim Incomplete Sentences": "Taglia Frasi Incomplete", "Include Newline": "<PERSON>ludi <PERSON> a Capo", "Misc. Settings": "Impostazioni varie", "Collapse Consecutive Newlines": "Riduci a capo consecutivi", "Trim spaces": "Taglia spazi", "Tokenizer": "Tokenizer", "Token Padding": "Riempimento token", "Start Reply With": "Inizia Risposta Con", "AI reply prefix": "Prefisso risposta AI", "Show reply prefix in chat": "<PERSON><PERSON> prefisso risposta in chat", "Non-markdown strings": "Stringhe non markdown", "separate with commas w/o space between": "separati con virgole senza spazio tra loro", "Custom Stopping Strings": "<PERSON><PERSON> di Stop Personalizzate", "JSON serialized array of strings": "Matrice serializzata JSON di stringhe", "Replace Macro in Custom Stopping Strings": "Sostituisci Macro in Stringhe di Arresto Personalizzate", "Auto-Continue": "Auto-continua", "Allow for Chat Completion APIs": "Consenti per API di completamento chat", "Target length (tokens)": "Lunghezza obiettivo (token)", "World Info": "Informazioni sul mondo", "Locked = World Editor will stay open": "Bloccato = L'Editor del Mondo rimarrà aperto", "Worlds/Lorebooks": "Mondi/Libri di Lore", "Active World(s) for all chats": "Mon<PERSON>/i Attivo/i per tutte le chat", "-- World Info not found --": "-- Informazioni sul Mondo non trovate --", "Global World Info/Lorebook activation settings": "Impostazioni di attivazione delle informazioni sul mondo globale/del libro di conoscenze", "Click to expand": "Clicca per espandere", "Scan Depth": "Profondità della scansione", "Context %": "Contesto %", "Budget Cap": "Limite di bilancio", "(0 = disabled)": "(0 = disabilitato)", "Scan chronologically until reached min entries or token budget.": "Esegui la scansione in ordine cronologico fino al raggiungimento delle voci minime o del budget dei token.", "Min Activations": "Attivazioni minime", "Max Depth": "Profondità massima", "(0 = unlimited, use budget)": "(0 = illimitato, utilizza il budget)", "Insertion Strategy": "Strategia di inserimento", "Sorted Evenly": "Ordinato Equamente", "Character Lore First": "Lore del Personaggio Prima", "Global Lore First": "Lore Globale Prima", "Entries can activate other entries by mentioning their keywords": "Le voci possono attivare altre voci menzionando le loro parole chiave", "Recursive Scan": "Scansione Ricorsiva", "Lookup for the entry keys in the context will respect the case": "La ricerca delle chiavi di ingresso nel contesto rispetterà la maiuscole e minuscole", "Case Sensitive": "Sensibile alle Maiuscole", "If the entry key consists of only one word, it would not be matched as part of other words": "Se la chiave di ingresso consiste in una sola parola, non verrà abbinata come parte di altre parole", "Match Whole Words": "Corrispondi parole intere", "Only the entries with the most number of key matches will be selected for Inclusion Group filtering": "Solo le voci con il maggior numero di corrispondenze chiave verranno selezionate per il filtraggio del gruppo di inclusione", "Use Group Scoring": "Utilizza il punteggio di gruppo", "Alert if your world info is greater than the allocated budget.": "Avvisa se le informazioni sul tuo mondo superano il budget assegnato.", "Alert On Overflow": "Avviso Su Overflow", "New": "Nuovo", "or": "o", "--- Pick to Edit ---": "--- <PERSON><PERSON><PERSON> da Modificare ---", "Rename World Info": "Rinomina Informazioni sul Mondo", "Open all Entries": "<PERSON><PERSON> le Voci", "Close all Entries": "<PERSON><PERSON> le Voci", "New Entry": "Nuova Voce", "Fill empty Memo/Titles with Keywords": "Rie<PERSON><PERSON> Me<PERSON>/<PERSON><PERSON> vuo<PERSON> con Pa<PERSON><PERSON>", "Import World Info": "Importa Informazioni sul Mondo", "Export World Info": "Esporta Informazioni sul Mondo", "Duplicate World Info": "Duplica Informazioni sul Mondo", "Delete World Info": "Elimina Informazioni sul Mondo", "Search...": "Cerca...", "Search": "Ricerca", "Priority": "Priorità", "Custom": "<PERSON><PERSON><PERSON><PERSON>", "Title A-Z": "Titolo A-Z", "Title Z-A": "<PERSON><PERSON>", "Tokens ↗": "Token ↗", "Tokens ↘": "Token ↘", "Depth ↗": "Profondità ↗", "Depth ↘": "Profondità ↘", "Order ↗": "Ordine ↗", "Order ↘": "Ordine ↘", "UID ↗": "UID ↗", "UID ↘": "UID ↘", "Trigger% ↗": "Trigger% ↗", "Trigger% ↘": "Trigger% ↘", "Refresh": "Aggiorna", "User Settings": "Impostazioni utente", "Simple": "Semplice", "Advanced": "<PERSON><PERSON><PERSON>", "UI Language": "<PERSON><PERSON>", "Account": "Account", "Admin Panel": "Pannello di Amministrazione", "Logout": "<PERSON>nne<PERSON><PERSON>", "Search Settings": "Impostazioni di Ricerca", "UI Theme": "Tema UI", "Import a theme file": "Importa un file di tema", "Export a theme file": "Esporta un file di tema", "Delete a theme": "Elimina un tema", "Update a theme file": "Aggiorna un file di tema", "Save as a new theme": "Salva come nuovo tema", "Avatar Style": "Stile avatar", "Circle": "Cerchio", "Square": "Quadrato", "Rectangle": "Rettangolo", "Chat Style:": "<PERSON><PERSON>t:", "Flat": "Piatto\nBolle\nDocumento", "Bubbles": "<PERSON><PERSON>", "Document": "Documento", "Specify colors for your theme.": "Specifica i colori per il tuo tema.", "Theme Colors": "Colori del tema", "Main Text": "Testo principale", "Italics Text": "Testo corsivo", "Underlined Text": "Testo sotto<PERSON>ato", "Quote Text": "Testo citazione", "Shadow Color": "Colore dell'ombra", "Chat Background": "Sfondo Chat", "UI Background": "Sfondo UI", "UI Border": "Bordo <PERSON>", "User Message Blur Tint": "Tonalità di Sfocatura del Messaggio Utente", "AI Message Blur Tint": "Tonalità di Sfocatura del Messaggio AI", "Chat Width": "<PERSON><PERSON><PERSON><PERSON>", "Width of the main chat window in % of screen width": "Larghezza della finestra di chat principale in % della larghezza dello schermo", "Font Scale": "Scala del carattere", "Font size": "Dimensione del font", "Blur Strength": "Intensità della sfocatura", "Blur strength on UI panels.": "Intensità della sfocatura sui pannelli dell'interfaccia utente.", "Text Shadow Width": "Larghezza dell'ombra del testo", "Strength of the text shadows": "Intensità delle ombre del testo", "Disables animations and transitions": "Disabilita animazioni e transizioni", "Reduced Motion": "<PERSON><PERSON><PERSON>", "removes blur from window backgrounds": "rimuove la sfocatura dagli sfondi delle finestre", "No Blur Effect": "<PERSON><PERSON><PERSON> effetto di sfocatura", "Remove text shadow effect": "<PERSON><PERSON><PERSON><PERSON> effetto ombra del testo", "No Text Shadows": "Nessuna ombra di testo", "Reduce chat height, and put a static sprite behind the chat window": "Riduci l'altezza della chat e metti uno sprite statico dietro la finestra della chat", "Waifu Mode": "<PERSON><PERSON>it<PERSON>", "Always show the full list of the Message Actions context items for chat messages, instead of hiding them behind '...'": "Mostra sempre l'elenco completo degli elementi di contesto delle Azioni Messaggio per i messaggi della chat, invece di nasconderli dietro '...'", "Auto-Expand Message Actions": "Espandi Automaticamente le Azioni Messaggio", "Alternative UI for numeric sampling parameters with fewer steps": "UI alternativa per i parametri di campionamento numerico con meno passaggi", "Zen Sliders": "<PERSON><PERSON><PERSON>", "Entirely unrestrict all numeric sampling parameters": "Rimuovi completamente tutte le restrizioni dai parametri di campionamento numerico", "Mad Lab Mode": "Modalità Mad Lab", "Time the AI's message generation, and show the duration in the chat log": "Misura il tempo di generazione del messaggio dell'AI e mostra la durata nel registro della chat", "Message Timer": "Timer del messaggio", "Show a timestamp for each message in the chat log": "Mostra un timestamp per ogni messaggio nel registro della chat", "Chat Timestamps": "Timestamp Chat", "Show an icon for the API that generated the message": "Mostra un'icona per l'API che ha generato il messaggio", "Model Icon": "Icona del modello", "Show sequential message numbers in the chat log": "Mostra numeri di messaggi sequenziali nel registro della chat", "Message IDs": "ID Messaggio", "Hide avatars in chat messages.": "Nascondi gli avatar nei messaggi di chat.", "Hide Chat Avatars": "Nascondi avatar di chat", "Show the number of tokens in each message in the chat log": "Mostra il numero di token in ogni messaggio nel registro della chat", "Show Message Token Count": "Mostra Conteggio Token Messaggio", "Single-row message input area. Mobile only, no effect on PC": "Area di input messaggio a una riga. Solo mobile, nessun effetto su PC", "Compact Input Area (Mobile)": "Area di Input Compatta (Mobile)", "In the Character Management panel, show quick selection buttons for favorited characters": "Nel pannello Gestione Personaggi, mostra pulsanti di selezione rapida per i personaggi preferiti", "Characters Hotswap": "Scambio rapido dei personaggi", "Enable magnification for zoomed avatar display.": "Abilita l'ingrandimento per la visualizzazione dell'avatar ingrandito.", "Avatar Hover Magnification": "Ingrandimento tramite passaggio del mouse sull'avatar", "Enables a magnification effect on hover when you display the zoomed avatar after clicking an avatar's image in chat.": "Abilita un effetto di ingrandimento al passaggio del mouse quando visualizzi l'avatar ingrandito dopo aver fatto clic sull'immagine di un avatar nella chat.", "Show tagged character folders in the character list": "Mostra cartelle dei personaggi contrassegnati nell'elenco dei personaggi", "Tags as Folders": "Tag come Cartelle", "Tags_as_Folders_desc": "Modifica recente: i tag devono essere contrassegnati come cartelle nel menu Gestione tag per apparire come tali. Clicca qui per visualizzarlo.", "Character Handling": "Gestione dei personaggi", "If set in the advanced character definitions, this field will be displayed in the characters list.": "Se impostato nelle definizioni avanzate dei personaggi, questo campo verrà visualizzato nell'elenco dei personaggi.", "Char List Subheader": "Sottointestazione elenco caratteri", "Character Version": "Versione del Personaggio", "Created by": "<PERSON><PERSON><PERSON> <PERSON>", "Use fuzzy matching, and search characters in the list by all data fields, not just by a name substring": "<PERSON>a corrispondenze fuzzy e cerca personaggi nell'elenco per tutti i campi dati, non solo per una sottostringa di nome", "Advanced Character Search": "Ricerca avanzata dei personaggi", "If checked and the character card contains a prompt override (System Prompt), use that instead": "Se selezionato e la scheda del personaggio contiene una sovrascrittura del prompt (Prompt di Sistema), usalo invece", "Prefer Character Card Prompt": "Preferisci Prompt della Scheda Personaggio", "If checked and the character card contains a jailbreak override (Post History Instruction), use that instead": "Se selezionato e la scheda del personaggio contiene una sovrascrittura jailbreak (Istruzione Storico Post), usalo invece", "Prefer Character Card Jailbreak": "Preferisci Jailbreak della Scheda Personaggio", "Avoid cropping and resizing imported character images. When off, crop/resize to 512x768": "Evita di ritagliare e ridimensionare le immagini dei personaggi importati. Quando è disattivato, ritaglia/ridimensiona a 512x768.", "Never resize avatars": "Non ridimensionare mai gli avatar", "Show actual file names on the disk, in the characters list display only": "Mostra i nomi file effettivi sul disco, solo nella visualizzazione dell'elenco dei personaggi", "Show avatar filenames": "Mostra nomi file avatar", "Prompt to import embedded card tags on character import. Otherwise embedded tags are ignored": "Prompt per importare tag della scheda incorporati all'importazione del personaggio. Altrimenti i tag incorporati vengono ignorati", "Import Card Tags": "Importa Tag della Scheda", "Hide character definitions from the editor panel behind a spoiler button": "Nascondi le definizioni del personaggio dal pannello dell'editor dietro a un pulsante spoiler", "Spoiler Free Mode": "Modalità Senza Spoiler", "Miscellaneous": "<PERSON><PERSON><PERSON>", "Reload and redraw the currently open chat": "Ricarica e ridisegna la chat attualmente aperta", "Reload Chat": "Ricarica Chat", "Debug Menu": "Menu di debug", "Smooth Streaming": "Streaming fluido", "Experimental feature. May not work for all backends.": "Funzionalità sperimentale. Potrebbe non funzionare per tutti i backend.", "Slow": "<PERSON><PERSON>", "Fast": "Veloce", "Play a sound when a message generation finishes": "Riproduci un suono quando la generazione del messaggio è completa", "Message Sound": "<PERSON><PERSON>", "Only play a sound when ST's browser tab is unfocused": "Riproduci un suono solo quando la scheda del browser di ST non è focalizzata", "Background Sound Only": "Solo suono di sfondo", "Reduce the formatting requirements on API URLs": "Riduci i requisiti di formattazione degli URL dell'API", "Relaxed API URLS": "URL API rilassati", "Ask to import the World Info/Lorebook for every new character with embedded lorebook. If unchecked, a brief message will be shown instead": "Chiedi di importare le Informazioni sul Mondo/Lorebook per ogni nuovo personaggio con lorebook incorporato. Se non selezionato, verrà mostrato invece un breve messaggio", "Lorebook Import Dialog": "Dialogo di importazione del lorebook", "Restore unsaved user input on page refresh": "Ripristina l'input utente non salvato al ricaricamento della pagina", "Restore User Input": "Ripristina l'input dell'utente", "Allow repositioning certain UI elements by dragging them. PC only, no effect on mobile": "Consenti il riposizionamento di determinati elementi UI trascinandoli. Solo PC, nessun effetto su mobile", "Movable UI Panels": "Pannelli UI mobili", "MovingUI preset. Predefined/saved draggable positions": "Preset MovingUI. Posizioni trascinabili predefinite/salvate", "MUI Preset": "Preset MUI", "Save movingUI changes to a new file": "Salva le modifiche di MovingUI in un nuovo file", "Reset MovingUI panel sizes/locations.": "Ripristina le <PERSON>/posizioni del pannello MovingUI.", "Apply a custom CSS style to all of the ST GUI": "Applica uno stile CSS personalizzato a tutta l'interfaccia grafica di ST", "Custom CSS": "CSS personalizzato", "Expand the editor": "Espandi l'editor", "Chat/Message Handling": "Gestione della chat/dei messaggi", "# Messages to Load": "#Msg. caricare", "The number of chat history messages to load before pagination.": "Il numero di messaggi della cronologia chat da caricare prima dell'impaginazione.", "(0 = All)": "(0 = <PERSON><PERSON>)", "Streaming FPS": "FPS Streaming", "Update speed of streamed text.": "Aggiorna la velocità del testo in streaming.", "Example Messages Behavior": "Comportamento dei messaggi di esempio", "Gradual push-out": "Push-out graduale", "Always include examples": "Includi sempre gli esempi", "Never include examples": "Non includere mai gli esempi", "Send on Enter": "Invia su Invio", "Disabled": "Disabilitato", "Automatic (PC)": "<PERSON>o (PC)", "Press Send to continue": "Premi Invia per continuare", "Show a button in the input area to ask the AI to continue (extend) its last message": "Mostra un pulsante nell'area di input per chiedere all'AI di continuare (estendere) il suo ultimo messaggio", "Quick 'Continue' button": "Pulsante 'Continua' rapido", "Show arrow buttons on the last in-chat message to generate alternative AI responses. Both PC and mobile": "Mostra pulsanti a freccia sull'ultimo messaggio in-chat per generare risposte alternative AI. Sia su PC che su mobile", "Swipes": "Sc<PERSON><PERSON>ent<PERSON>", "Allow using swiping gestures on the last in-chat message to trigger swipe generation. Mobile only, no effect on PC": "Consenti l'uso di gesti di scorrimento sull'ultimo messaggio in-chat per attivare la generazione di scorrimento. Solo mobile, nessun effetto su PC", "Gestures": "<PERSON><PERSON><PERSON>", "Auto-load Last Chat": "Caricamento automatico dell'ultima chat", "Auto-scroll Chat": "Chat di scorrimento automatico", "Save edits to messages without confirmation as you type": "Salva le modifiche ai messaggi senza conferma mentre digiti", "Auto-save Message Edits": "Salvataggio automatico delle modifiche ai messaggi", "Confirm message deletion": "Conferma eliminazione messaggio", "Auto-fix Markdown": "Correzione automatica di Markdown", "Disallow embedded media from other domains in chat messages": "Non consentire contenuti multimediali incorporati da altri domini nei messaggi di chat.", "Forbid External Media": "Vietare i media esterni", "Allow {{char}}: in bot messages": "Consenti {{char}}: nei messaggi del bot", "Allow {{user}}: in bot messages": "Consenti {{user}}: nei messaggi del bot", "Skip encoding  and  characters in message text, allowing a subset of HTML markup as well as Markdown": "Salta la codifica di e e caratteri nel testo del messaggio, consentendo un subset di markup HTML così come Markdown", "Show tags in responses": "Mostra tag nelle risposte", "Allow AI messages in groups to contain lines spoken by other group members": "Consenti ai messaggi dell'IA nei gruppi di contenere righe pronunciate da altri membri del gruppo", "Relax message trim in Groups": "Rilassa il taglio dei messaggi nei Gruppi", "Log prompts to console": "Registra i prompt sulla console", "Requests logprobs from the API for the Token Probabilities feature": "Richiede logprobs dall'API per la funzionalità di Probabilità dei Token", "Request token probabilities": "Richiesta delle probabilità dei token", "Automatically reject and re-generate AI message based on configurable criteria": "Rifiuta e rigenera automaticamente il messaggio AI in base a criteri configurabili", "Auto-swipe": "Auto-swipe", "Enable the auto-swipe function. Settings in this section only have an effect when auto-swipe is enabled": "Abilita la funzione di auto-swipe. Le impostazioni in questa sezione hanno effetto solo quando l'auto-swipe è abilitato", "Minimum generated message length": "Lunghezza minima del messaggio generato", "If the generated message is shorter than this, trigger an auto-swipe": "Se il messaggio generato è più breve di questo, attiva un'automatica rimozione", "Blacklisted words": "<PERSON><PERSON><PERSON> in blacklist", "words you dont want generated separated by comma ','": "parole che non vuoi generate separate da virgola ','", "Blacklisted word count to swipe": "Numero di parole in blacklist per attivare un'automatica rimozione", "Minimum number of blacklisted words detected to trigger an auto-swipe": "Numero minimo di parole in blacklist rilevate per attivare un'automatica rimozione", "AutoComplete Settings": "Impostazioni di completamento automatico", "Automatically hide details": "Nascondi automaticamente i dettagli", "Determines how entries are found for autocomplete.": "Determina il modo in cui vengono trovate le voci per il completamento automatico.", "Autocomplete Matching": "Corrispondenza", "Starts with": "Inizia con", "Includes": "Include", "Fuzzy": "Sfocato", "Sets the style of the autocomplete.": "Imposta lo stile del completamento automatico.", "Autocomplete Style": "Stile", "Follow Theme": "<PERSON><PERSON><PERSON> il tema", "Dark": "<PERSON><PERSON><PERSON>", "Sets the font size of the autocomplete.": "Imposta la dimensione del carattere del completamento automatico.", "Sets the width of the autocomplete.": "Imposta la larghezza del completamento automatico.", "Autocomplete Width": "<PERSON><PERSON><PERSON><PERSON>", "chat input box": "casella di input della chat", "entire chat width": "tutta la larghezza della chat", "full window width": "larghezza completa della finestra", "STscript Settings": "Impostazioni STscript", "Sets default flags for the STscript parser.": "Imposta i flag predefiniti per il parser STscript.", "Parser Flags": "Flag del parser", "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.": "Passare a un'escape più rigorosa, consentendo l'escape di tutti i caratteri di delimitazione con una barra rovesciata e anche delle barre rovesciate.", "STRICT_ESCAPING": "STRICT_ESCAPING", "Replace all {{getvar::}} and {{getglobalvar::}} macros with scoped variables to avoid double macro substitution.": "Sostituisci tutte le macro {{getvar::}} e {{getglobalvar::}} con variabili con ambito per evitare la doppia sostituzione di macro.", "REPLACE_GETVAR": "SOSTITUISCI_GETVAR", "Change Background Image": "Cambia Immagine di Sfondo", "Filter": "Filtro", "Automatically select a background based on the chat context": "Seleziona automaticamente uno sfondo in base al contesto della chat", "Auto-select": "Selezione automatica", "System Backgrounds": "Sfondi di sistema", "Chat Backgrounds": "Sfondi Chat", "bg_chat_hint_1": "Sfondi chat generati con", "bg_chat_hint_2": "l'estensione apparirà qui.", "Extensions": "Estensioni", "Notify on extension updates": "Notifica sugli aggiornamenti dell'estensione", "Manage extensions": "Gestisci estensioni", "Import Extension From Git Repo": "Importa estensione dal repository Git", "Install extension": "Installa estensione", "Extras API:": "API extra:", "Auto-connect": "Auto-connetti", "Extras API URL": "URL dell'API extra", "Extras API key (optional)": "Chiave API extra (opzionale)", "Persona Management": "Gestione Personaggio", "How do I use this?": "Come lo uso?", "Click for stats!": "<PERSON>licca per le statistiche!", "Usage Stats": "Statistiche d'uso", "Backup your personas to a file": "Esegui il backup delle tue personalità su un file", "Backup": "Backup", "Restore your personas from a file": "R<PERSON><PERSON><PERSON> le tue personalità da un file", "Restore": "R<PERSON><PERSON><PERSON>", "Create a dummy persona": "<PERSON><PERSON> una persona fittizia", "Create": "<PERSON><PERSON>", "Toggle grid view": "Attiva/Disattiva la vista a griglia", "No persona description": "[Nessuna descrizione]", "Name": "Nome", "Enter your name": "Inser<PERSON>ci il tuo nome", "Click to set a new User Name": "Fai clic per impostare un nuovo Nome Utente", "Click to lock your selected persona to the current chat. Click again to remove the lock.": "Fai clic per bloccare il personaggio selezionato alla chat attuale. Fai di nuovo clic per rimuovere il blocco.", "Click to set user name for all messages": "Fai clic per impostare il nome utente per tutti i messaggi", "Persona Description": "Descrizione Personaggio", "Example: [{{user}} is a 28-year-old Romanian cat girl.]": "Esempio: [{{user}} è una ragazza gatta rumena di 28 anni.]", "Tokens persona description": "Descrizione della persona dei token", "Position:": "Posizione:", "In Story String / Prompt Manager": "In Stringa della Storia / Gestore di Prompt", "Top of Author's Note": "In Alto alla Nota dell'Autore", "Bottom of Author's Note": "In Basso alla Nota dell'Autore", "In-chat @ Depth": "In chat @ Profondità", "Depth:": "Profondità:", "Role:": "Ruolo:", "System": "Sistema", "User": "Utente", "Assistant": "<PERSON><PERSON><PERSON>", "Show notifications on switching personas": "Mostra notifiche durante il cambio di personaggi", "Character Management": "Gestione Personaggio", "Locked = Character Management panel will stay open": "Bloccato = Il pannello di Gestione Personaggio rimarrà aperto", "Select/Create Characters": "Seleziona/<PERSON><PERSON>", "Favorite characters to add them to HotSwaps": "Aggiungi personaggi preferiti per aggiungerli a HotSwaps", "Token counts may be inaccurate and provided just for reference.": "I conteggi dei token possono essere inaccurati e forniti solo per riferimento.", "Total tokens": "Gettoni totali", "Calculating...": "Calcolo...", "Tokens": "Token", "Permanent tokens": "Gettoni permanenti", "Permanent": "Permanente", "About Token 'Limits'": "Informazioni sui \"Limiti\" dei token", "Toggle character info panel": "Attiva/disattiva il pannello delle informazioni sul personaggio", "Name this character": "Dai un nome a questo personaggio", "extension_token_counter": "Gettoni:", "Click to select a new avatar for this character": "Fai clic per selezionare una nuova immagine del profilo per questo personaggio", "Add to Favorites": "Aggiungi ai Preferiti", "Advanced Definition": "Definizione Avanzata", "Character Lore": "Storia del Personaggio", "Chat Lore": "Chatta Lore", "Export and Download": "Esporta e Scarica", "Duplicate Character": "<PERSON><PERSON><PERSON>", "Create Character": "<PERSON><PERSON>", "Delete Character": "<PERSON><PERSON>", "More...": "Altro...", "Link to World Info": "Collegamento alle Informazioni del Mondo", "Import Card Lore": "Importa Lore della Carta", "Scenario Override": "Sostituzione Scenario", "Convert to Persona": "Converti in Persona", "Rename": "Rinomina", "Link to Source": "Collegamento alla fonte", "Replace / Update": "Sostituisci/Aggiorna", "Import Tags": "Importa tag", "Search / Create Tags": "Cerca / Crea Tag", "View all tags": "Visualizza tutti i Tag", "Creator's Notes": "Note del Creatore", "Show / Hide Description and First Message": "Mostra / Nascondi Descrizione e Primo Messaggio", "Character Description": "Descrizione del Personaggio", "Click to allow/forbid the use of external media for this character.": "Fai clic per consentire/vietare l'uso di media esterni per questo personaggio.", "Ext. Media": "Est. Media", "Describe your character's physical and mental traits here.": "Descrivi qui le caratteristiche fisiche e mentali del tuo personaggio.", "First message": "Primo messaggio", "Click to set additional greeting messages": "Fai clic per impostare ulteriori messaggi di saluto", "Alt. Greetings": "Alt. Saluti", "This will be the first message from the character that starts every chat.": "Questo sarà il primo messaggio del personaggio che avvia ogni chat.", "Group Controls": "Controlli di gruppo", "Chat Name (Optional)": "Nome della Chat (Opzionale)", "Click to select a new avatar for this group": "Fai clic per selezionare una nuova immagine del profilo per questo gruppo", "Group reply strategy": "Strategia di risposta di gruppo", "Natural order": "Ordine naturale", "List order": "Ordine della lista", "Group generation handling mode": "Modalità di gestione della generazione di gruppi", "Swap character cards": "Scambia le carte personaggio", "Join character cards (exclude muted)": "Unisci le carte dei personaggi (escludi l'audio disattivato)", "Join character cards (include muted)": "Unisci le carte dei personaggi (includi disattivate)", "Inserted before each part of the joined fields.": "Inserito prima di ogni parte dei campi uniti.", "Join Prefix": "Unisciti al prefisso", "When 'Join character cards' is selected, all respective fields of the characters are being joined together.This means that in the story string for example all character descriptions will be joined to one big text.If you want those fields to be separated, you can define a prefix or suffix here.This value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)": "Quando viene selezionato \"Unisci carte personaggio\", tutti i rispettivi campi dei personaggi verranno uniti.\rCiò significa che nella stringa della storia, ad esempio, tutte le descrizioni dei personaggi verranno unite in un unico grande testo.\rSe desideri che questi campi siano separati, puoi definire un prefisso o un suffisso qui.\r\rQuesto valore supporta le macro normali e sostituirà anche {{char}} con il nome del carattere rilevante e <FIELDNAME> con il nome della parte (es.: descrizione, personalità, scenario, ecc.)", "Inserted after each part of the joined fields.": "Inserito dopo ogni parte dei campi uniti.", "Join Suffix": "Unisciti al suffisso", "Set a group chat scenario": "Imposta uno scenario di chat di gruppo", "Click to allow/forbid the use of external media for this group.": "Fare clic per consentire/vietare l'uso di media esterni per questo gruppo.", "Restore collage avatar": "Ripristina l'immagine del profilo a collage", "Allow self responses": "Consenti risposte automatiche", "Auto Mode": "Modalità automatica", "Auto Mode delay": "<PERSON>rdo modalità automatica", "Hide Muted Member Sprites": "Nascondi gli sprite dei membri disattivati", "Current Members": "<PERSON><PERSON><PERSON> at<PERSON>", "Add Members": "Aggiungi membri", "Create New Character": "Crea un Nuovo Personaggio", "Import Character from File": "<PERSON><PERSON><PERSON>", "Import content from external URL": "Importa contenuto da URL esterno", "Create New Chat Group": "Crea un Nuovo Gruppo di Chat", "Characters sorting order": "Ordine di Ordinamento dei Personaggi", "A-Z": "A-Z", "Z-A": "Z-A", "Newest": "<PERSON><PERSON>", "Oldest": "<PERSON><PERSON>", "Favorites": "Preferiti", "Recent": "<PERSON><PERSON>", "Most chats": "<PERSON><PERSON>", "Least chats": "<PERSON><PERSON>", "Most tokens": "La maggior parte dei token", "Least tokens": "Meno token", "Random": "Casuale", "Toggle character grid view": "Attiva/disattiva visualizzazione griglia personaggi", "Bulk_edit_characters": "Modifica personaggi in blocco", "Bulk select all characters": "Seleziona in blocco tutti i personaggi", "Bulk delete characters": "Elimina personaggi in blocco", "popup-button-save": "<PERSON><PERSON>", "popup-button-yes": "SÌ", "popup-button-no": "NO", "popup-button-cancel": "<PERSON><PERSON><PERSON>", "popup-button-import": "Importare", "Advanced Definitions": "Definizioni avanzate", "Prompt Overrides": "Sostituzioni richieste", "(For Chat Completion and Instruct Mode)": "(Per il completamento della chat e la modalità istruzione)", "Insert {{original}} into either box to include the respective default prompt from system settings.": "Inserisci {{originale}} in uno dei due riquadri per includere il prompt predefinito corrispondente dalle impostazioni di sistema.", "Main Prompt": "Prompt Principale", "Any contents here will replace the default Main Prompt used for this character. (v2 spec: system_prompt)": "Ogni contenuto qui sostituirà il prompt principale predefinito utilizzato per questo personaggio. (spec v2: system_prompt)", "Any contents here will replace the default Jailbreak Prompt used for this character. (v2 spec: post_history_instructions)": "Ogni contenuto qui sostituirà il prompt di Jailbreak predefinito utilizzato per questo personaggio. (spec v2: post_history_instructions)", "Creator's Metadata (Not sent with the AI prompt)": "Metadati del Creatore (Non inviati con il prompt AI)", "Creator's Metadata": "Metadati del creatore", "(Not sent with the AI Prompt)": "(Non inviato con il prompt AI)", "Everything here is optional": "Tutto qui è facoltativo", "(Botmaker's name / Contact Info)": "(Nome del Creatore del Bot / Informazioni di Contatto)", "(If you want to track character versions)": "(Se vuoi tracciare le versioni del personaggio)", "(Describe the bot, give use tips, or list the chat models it has been tested on. This will be displayed in the character list.)": "(<PERSON><PERSON><PERSON><PERSON> il bot, dai suggerimenti sull'uso o elenca i modelli di chat su cui è stato testato. Questo verrà visualizzato nell'elenco dei personaggi.)", "Tags to Embed": "Tag da Incorporare", "(Write a comma-separated list of tags)": "(Sc<PERSON>vi un elenco di tag separati da virgole)", "Personality summary": "Sommario della personalità", "(A brief description of the personality)": "(Una breve descrizione della personalità)", "Scenario": "<PERSON><PERSON><PERSON>", "(Circumstances and context of the interaction)": "(Circostanze e contesto dell'interazione)", "Character's Note": "Nota del personaggio", "(Text to be inserted in-chat @ designated depth and role)": "(<PERSON><PERSON> da inserire in chat @ profondità e ruolo designati)", "@ Depth": "@ Profondità", "Role": "<PERSON><PERSON><PERSON>", "Talkativeness": "Loquacità", "How often the character speaks in group chats!": "Quanto spesso il personaggio parla nelle chat di gruppo!", "How often the character speaks in": "Quanto spesso parla il personaggio", "group chats!": "chat di gruppo!", "Shy": "Timido", "Normal": "Normale", "Chatty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Examples of dialogue": "Esempi di dialogo", "Important to set the character's writing style.": "Importante impostare lo stile di scrittura del personaggio.", "(Examples of chat dialog. Begin each example with START on a new line.)": "(Esempi di dialogo in chat. Inizia ogni esempio con START su una nuova riga.)", "Save": "<PERSON><PERSON>", "Chat History": "Cronologia Chat", "Import Chat": "Importa chat", "Copy to system backgrounds": "Copia negli sfondi di sistema", "Rename background": "Rinominare lo sfondo", "Lock": "Serratura", "Unlock": "Sbloccare", "Delete background": "Elimina lo sfondo", "Chat Scenario Override": "Sostituzione dello scenario di chat", "Remove": "<PERSON><PERSON><PERSON><PERSON>", "Type here...": "Scrivi qui...", "Chat Lorebook": "Chatta Lorebook per", "Chat Lorebook for": "Chatta Lorebook per", "chat_world_template_txt": "Un'Info mondiale selezionata sarà associata a questa chat. Quando si genera una risposta AI, questa verrà combinata con le voci dei lorebook globali e dei personaggi.", "Select a World Info file for": "Seleziona un file di Informazioni sul Mondo per", "Primary Lorebook": "Libro di Storia Principale", "A selected World Info will be bound to this character as its own Lorebook.": "Un'Informazione Mondiale selezionata sarà vincolata a questo personaggio come il suo proprio Libro di Storia.", "When generating an AI reply, it will be combined with the entries from a global World Info selector.": "Quando viene generata una risposta AI, sarà combinata con le voci da un selettore globale di Informazioni sul Mondo.", "Exporting a character would also export the selected Lorebook file embedded in the JSON data.": "L'esportazione di un personaggio comporterà anche l'esportazione del file di Libro di Storia selezionato incorporato nei dati JSON.", "Additional Lorebooks": "Libri di Storia Aggiuntivi", "Associate one or more auxillary Lorebooks with this character.": "Associa uno o più Libri di Storia ausiliari a questo personaggio.", "NOTE: These choices are optional and won't be preserved on character export!": "NOTA: Queste scelte sono opzionali e non verranno conservate nell'esportazione del personaggio!", "Rename chat file": "Rinomina il file di chat", "Export JSONL chat file": "Esporta file di chat JSONL", "Download chat as plain text document": "Scarica la chat come documento di testo semplice", "Delete chat file": "Elimina il file di chat", "Use tag as folder": "Contrassegna come cartella", "Delete tag": "Elimina il tag", "Entry Title/Memo": "Titolo/Memo dell'Ingresso", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized❌ Disabled": "Stato della voce WI:\r🔵 Costante\r🟢 Normale\r🔗 Vettorializzato\r❌Disabili", "WI_Entry_Status_Constant": "Costante", "WI_Entry_Status_Normal": "Normale", "WI_Entry_Status_Vectorized": "Vetto<PERSON><PERSON><PERSON><PERSON>", "WI_Entry_Status_Disabled": "Disabilitato", "T_Position": "↑Car: prima delle definizioni del personaggio\n↓Car: dopo le definizioni del personaggio\n↑AN: prima delle note dell'autore\n↓AN: dopo le note dell'autore\n@D: alla profondità", "Before Char Defs": "Prima delle Definizioni del Personaggio", "After Char Defs": "Dopo le Definizioni del Personaggio", "Before EM": "↑EM", "After EM": "↓EM", "Before AN": "Prima delle AN", "After AN": "<PERSON><PERSON>", "at Depth System": "@D ⚙️", "at Depth User": "@D 👤", "at Depth AI": "@D 🤖", "Depth": "Profondità", "Order:": "Ordine:", "Order": "Ordine:", "Trigger %:": "Grilletto %:", "Probability": "Probabilità", "Duplicate world info entry": "Voce di informazioni sul mondo duplicata", "Delete world info entry": "Elimina la voce di informazioni mondiali", "Comma separated (required)": "Separato da virgole (richiesto)", "Primary Keywords": "<PERSON><PERSON><PERSON>", "Keywords or Regexes": "Parole chiave o espressioni regolari", "Comma separated list": "Elenco separato da virgole", "Switch to plaintext mode": "Passa alla modalità testo normale", "Logic": "Logica", "AND ANY": "E QUALSIASI", "AND ALL": "E TUTTI", "NOT ALL": "NON TUTTI", "NOT ANY": "NESSUNO", "(ignored if empty)": "(ignorato se vuoto)", "Optional Filter": "Filtro Opzionale", "Keywords or Regexes (ignored if empty)": "Parole chiave o espressioni regolari (ignorate se vuote)", "Comma separated list (ignored if empty)": "Elenco separato da virgole (ignorato se vuoto)", "Use global setting": "Usa l'impostazione globale", "Case-Sensitive": "Sensibile alle maiuscole/minuscole", "Yes": "Sì", "No": "No", "Can be used to automatically activate Quick Replies": "<PERSON><PERSON>ò essere utilizzato per attivare automaticamente le risposte rapide", "Automation ID": "Identificativo dell'automazione", "( None )": "( <PERSON><PERSON><PERSON> )", "Content": "<PERSON><PERSON><PERSON>", "Exclude from recursion": "Esc<PERSON><PERSON> dalla ricorsione", "Prevent further recursion (this entry will not activate others)": "Prevenire ulteriore ricorsione (questa voce non ne attiverà altre)", "Delay until recursion (this entry can only be activated on recursive checking)": "<PERSON><PERSON> fino alla ricorsione (questa voce può essere attivata solo durante il controllo ricorsivo)", "What this keyword should mean to the AI, sent verbatim": "<PERSON><PERSON> do<PERSON>bbe significare questa parola chiave per l'AI, inviata testualmente", "Filter to Character(s)": "Filtra per Personaggio(i)", "Character Exclusion": "Esclusione Personaggio", "-- Characters not found --": "-- <PERSON><PERSON><PERSON> non trovati --", "Inclusion Group": "Gruppo di Inclusione", "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group": "I gruppi di inclusione garantiscono che venga attivata solo una voce di un gruppo alla volta, se ne vengono attivate più.\rSupporta più gruppi separati da virgole.\r\rDocumentazione: World Info – Gruppo Inclusione", "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.": "Dai priorità a questa voce: se selezionata, questa voce ha la priorità tra tutte le selezioni. Se vengono date priorità a più voci, viene scelta quella con l'\"Ordine\" più alto.", "Only one entry with the same label will be activated": "Sa<PERSON><PERSON> attivato solo un unico ingresso con lo stesso etichetta", "A relative likelihood of entry activation within the group": "Una probabilità relativa di attivazione dell'ingresso all'interno del gruppo", "Group Weight": "Peso del gruppo", "Selective": "Selettivo", "Use Probability": "Usa Probabilità", "Add Memo": "Aggiungi promemoria", "Text or token ids": "Testo o [ID token]", "close": "vicino", "prompt_manager_edit": "Modificare", "prompt_manager_name": "Nome", "A name for this prompt.": "Un nome per questo prompt.", "To whom this message will be attributed.": "A chi verrà attribuito questo messaggio.", "AI Assistant": "Assistente AI", "prompt_manager_position": "Posizione", "Injection position. Next to other prompts (relative) or in-chat (absolute).": "Posizione di iniezione. Accanto ad altri suggerimenti (relativo) o in chat (assoluto).", "prompt_manager_relative": "<PERSON><PERSON><PERSON>", "prompt_manager_depth": "Profondità", "Injection depth. 0 = after the last message, 1 = before the last message, etc.": "Profondità di iniezione. 0 = dopo l'ultimo messaggio, 1 = prima dell'ultimo messaggio, ecc.", "Prompt": "Prompt", "The prompt to be sent.": "La richiesta da inviare.", "This prompt cannot be overridden by character cards, even if overrides are preferred.": "Questo prompt non può essere sostituito dalle schede personaggio, anche se si preferisce sostituirlo.", "prompt_manager_forbid_overrides": "Vieta sostituzioni", "reset": "R<PERSON><PERSON><PERSON>", "save": "salva", "This message is invisible for the AI": "Questo messaggio è invisibile per l'IA", "Message Actions": "Azioni del messaggio", "Translate message": "T<PERSON><PERSON>i messaggio", "Generate Image": "Genera Immagine", "Narrate": "Narrare", "Exclude message from prompts": "Escludi messaggio dalle richieste", "Include message in prompts": "<PERSON>ludi messaggio nelle richieste", "Embed file or image": "Incorpora file o immagine", "Create checkpoint": "Crea checkpoint", "Create Branch": "Crea Branch", "Copy": "Copia", "Open checkpoint chat": "Apri la chat del checkpoint", "Edit": "Modifica", "Confirm": "Conferma", "Copy this message": "Copia questo messaggio", "Delete this message": "Elimina questo messaggio", "Move message up": "Sposta il messaggio verso l'alto", "Move message down": "Sposta il messaggio verso il basso", "Enlarge": "Ingrandire", "Welcome to SillyTavern!": "Benvenuti a Silly Tavern!", "welcome_message_part_1": "Leggi il", "welcome_message_part_2": "Documentazione ufficiale", "welcome_message_part_3": null, "welcome_message_part_4": "Tipo", "welcome_message_part_5": "nella chat per comandi e macro.", "welcome_message_part_6": "Ad<PERSON><PERSON> al", "Discord server": "Server Discordia", "welcome_message_part_7": "per info e annunci.", "SillyTavern is aimed at advanced users.": "SillyTavern è rivolto agli utenti avanzati.", "If you're new to this, enable the simplified UI mode below.": "Se sei nuovo a questo, abilita la modalità interfaccia utente semplificata di seguito.", "Change it later in the 'User Settings' panel.": "Modificalo successivamente nel pannello \"Impostazioni utente\".", "Enable simple UI mode": "Abilita la modalità interfaccia utente semplice", "Looking for AI characters?": "Cerchi personaggi IA?", "onboarding_import": "Importare", "from supported sources or view": "da fonti o visualizzazioni supportate", "Sample characters": "<PERSON><PERSON><PERSON> campione", "Your Persona": "Il Tuo Personaggio", "Before you get started, you must select a persona name.": "Prima di iniziare, devi selezionare un nome per la persona.", "welcome_message_part_8": "Questo può essere modificato in qualsiasi momento tramite il", "welcome_message_part_9": "icona.", "Persona Name:": "<PERSON><PERSON> della persona:", "Temporarily disable automatic replies from this character": "Disabilita temporaneamente le risposte automatiche da questo personaggio", "Enable automatic replies from this character": "Abilita risposte automatiche da questo personaggio", "Trigger a message from this character": "Scatenare un messaggio da questo personaggio", "Move up": "Sposta verso l'alto", "Move down": "Sposta verso il basso", "View character card": "Visualizza la carta del personaggio", "Remove from group": "<PERSON><PERSON><PERSON><PERSON> dal gruppo", "Add to group": "Aggiungi al gruppo", "Alternate Greetings": "Saluti alternativi", "Alternate_Greetings_desc": "Questi verranno visualizzati come passaggi sul primo messaggio quando si avvia una nuova chat.\n                I membri del gruppo possono selezionarne uno per avviare la conversazione.", "Alternate Greetings Hint": "Fare clic sul pulsante per iniziare!", "(This will be the first message from the character that starts every chat)": "(<PERSON><PERSON> sarà il primo messaggio del personaggio che avvia ogni chat)", "Forbid Media Override explanation": "Capacità del personaggio/gruppo attuale di utilizzare media esterni nelle chat.", "Forbid Media Override subtitle": "Media: immagini, video, audio. Esterno: non ospitato sul server locale.", "Always forbidden": "Sempre proibito", "Always allowed": "sempre permesso", "View contents": "Visualizza i contenuti", "Remove the file": "Rimuovere il file", "Unique to this chat": "Unico per questa chat", "Checkpoints inherit the Note from their parent, and can be changed individually after that.": "I checkpoint ereditano la nota dal genitore e successivamente possono essere modificati individualmente.", "Include in World Info Scanning": "Includi nella scansione delle informazioni sul mondo", "Before Main Prompt / Story String": "Prima della stringa del prompt principale/della storia", "After Main Prompt / Story String": "Dopo il prompt principale/stringa della storia", "as": "COME", "Insertion Frequency": "Frequenza di inserimento", "(0 = Disable, 1 = Always)": "(0 = Disabilita, 1 = Sempre)", "User inputs until next insertion:": "Input dell'utente fino al prossimo inserimento:", "Character Author's Note (Private)": "Nota dell'autore del personaggio (privata)", "Won't be shared with the character card on export.": "Non verrà condiviso con la scheda personaggio durante l'esportazione.", "Will be automatically added as the author's note for this character. Will be used in groups, but can't be modified when a group chat is open.": "Verrà aggiunto automaticamente come nota dell'autore per questo personaggio. Verrà utilizzato nei gruppi, ma non può essere modificato quando è aperta una chat di gruppo.", "Use character author's note": "Usa la nota dell'autore del personaggio", "Replace Author's Note": "Sostituisci la nota dell'autore", "Default Author's Note": "Nota dell'autore predefinita", "Will be automatically added as the Author's Note for all new chats.": "V<PERSON>rà aggiunto automaticamente come Nota dell'autore per tutte le nuove chat.", "Chat CFG": "Chatta CFG", "1 = disabled": "1 = disabilitato", "write short replies, write replies using past tense": "scrivere risposte brevi, scrivere risposte utilizzando il passato", "Positive Prompt": "Suggerimento positivo", "Use character CFG scales": "Usa le scale CFG dei personaggi", "Character CFG": "Carattere CFG", "Will be automatically added as the CFG for this character.": "Verrà aggiunto automaticamente come CFG per questo personaggio.", "Global CFG": "CFG globale", "Will be used as the default CFG options for every chat unless overridden.": "Verranno utilizzate come opzioni CFG predefinite per ogni chat a meno che non vengano sovrascritte.", "CFG Prompt Cascading": "Richiesta CFG a cascata", "Combine positive/negative prompts from other boxes.": "Combina suggerimenti positivi/negativi da altre caselle.", "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.": "<PERSON> esem<PERSON>, se<PERSON><PERSON><PERSON><PERSON> le caselle chat, globale e carattere si combinano tutti i suggerimenti negativi in ​​una stringa separata da virgole.", "Always Include": "<PERSON>ludi sempre", "Chat Negatives": "<PERSON><PERSON> negativi", "Character Negatives": "Aspetti negativi dei personaggi", "Global Negatives": "Negativi globali", "Custom Separator:": "Separatore <PERSON>:", "Insertion Depth:": "Profondità di inserimento:", "Token Probabilities": "Probabilità dei token", "Select a token to see alternatives considered by the AI.": "Seleziona un token per visualizzare le alternative prese in considerazione dall'IA.", "Not connected to API!": "Non connesso all'API!", "Type a message, or /? for help": "Digita un messaggio o /? per un aiuto", "Continue script execution": "Continua l'esecuzione dello script", "Pause script execution": "Sospende l'esecuzione dello script", "Abort script execution": "Interrompe l'esecuzione dello script", "Abort request": "<PERSON><PERSON><PERSON>a", "Continue the last message": "Continua l'ultimo messaggio", "Send a message": "Invia un messaggio", "Close chat": "<PERSON><PERSON> chat", "Toggle Panels": "Attiva/Disattiva pannelli", "Back to parent chat": "<PERSON>na alla chat principale", "Save checkpoint": "Salva il punto di controllo", "Convert to group": "Converti in gruppo", "Start new chat": "Avvia nuova chat", "Manage chat files": "Gestisci file di chat", "Delete messages": "<PERSON><PERSON>", "Regenerate": "R<PERSON>ner<PERSON>", "Ask AI to write your message for you": "Chiedi all'IA di scrivere il tuo messaggio per te", "Impersonate": "Imitare", "Continue": "Continua", "Bind user name to that avatar": "Associa il nome utente a quell'avatar", "Change persona image": "Cambia immagine della persona", "Select this as default persona for the new chats.": "<PERSON><PERSON><PERSON>na questo come persona predefinita per le nuove chat.", "Delete persona": "Elimina persona", "These characters are the winners of character design contests and have outstandable quality.": "Questi personaggi sono i vincitori dei concorsi di design dei personaggi e hanno una qualità eccezionale.", "Contest Winners": "Vincitori del concorso", "These characters are the finalists of character design contests and have remarkable quality.": "Questi personaggi sono i finalisti dei concorsi di design dei personaggi e hanno una qualità notevole.", "Featured Characters": "<PERSON><PERSON><PERSON> in primo piano", "Attach a File": "Allega un file", "Open Data Bank": "Apri Banca Dati", "Enter a URL or the ID of a Fandom wiki page to scrape:": "Inserisci un URL o l'ID di una pagina wiki di Fandom da analizzare:", "Examples:": "Esempi:", "Example:": "Esempio:", "Single file": "<PERSON> singolo", "All articles will be concatenated into a single file.": "Tutti gli articoli verranno concatenati in un unico file.", "File per article": "File per articolo", "Each article will be saved as a separate file.": "Non consigliato. Ogni articolo verrà salvato come file separato.", "Data Bank": "Banca dati", "These files will be available for extensions that support attachments (e.g. Vector Storage).": "Questi file saranno disponibili per le estensioni che supportano gli allegati (ad esempio Vector Storage).", "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.": "Tipi di file supportati: testo normale, PDF, Markdown, HTML, EPUB.", "Drag and drop files here to upload.": "Trascina e rilascia i file qui per caricarli.", "Date (Newest First)": "Data (prima il più recente)", "Date (Oldest First)": "Data (prima il più vecchio)", "Name (A-Z)": "Nome (A-Z)", "Name (Z-A)": "Nome (Z-A)", "Size (Smallest First)": "Dimensioni (prima la più piccola)", "Size (Largest First)": "Dimensioni (prima la più grande)", "Bulk Edit": "Modifica collettiva", "Select All": "Se<PERSON><PERSON>na tutto", "Select None": "Non selezionare niente", "Global Attachments": "Allegati globali", "These files are available for all characters in all chats.": "Questi file sono disponibili per tutti i personaggi in tutte le chat.", "Character Attachments": "Allegati ai personaggi", "These files are available the current character in all chats they are in.": "Questi file sono disponibili per il personaggio corrente in tutte le chat in cui si trova.", "Saved locally. Not exported.": "Salvato localmente. Non esportato.", "Chat Attachments": "Allegati della chat", "These files are available to all characters in the current chat.": "Questi file sono disponibili per tutti i personaggi nella chat corrente.", "Enter a base URL of the MediaWiki to scrape.": "Inserisci un URL di base del MediaWiki da cui effettuare lo scraping.", "Don't include the page name!": "Non includere il nome della pagina!", "Enter web URLs to scrape (one per line):": "Inserisci gli URL web da analizzare (uno per riga):", "Enter a video URL to download its transcript.": "Inserisci l'URL o l'ID del video per scaricarne la trascrizione.", "Expression API": "Locale\nExtra\nLLM", "ext_sum_with": "<PERSON><PERSON><PERSON><PERSON> con:", "ext_sum_main_api": "API principale", "ext_sum_current_summary": "Riepilogo attuale:", "ext_sum_restore_previous": "<PERSON><PERSON><PERSON><PERSON>e", "ext_sum_memory_placeholder": "Il riepilogo verrà generato qui...", "Trigger a summary update right now.": "<PERSON><PERSON><PERSON><PERSON>a", "ext_sum_force_text": "<PERSON><PERSON><PERSON><PERSON>a", "Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API).": "Disattiva gli aggiornamenti di riepilogo automatici. Durante la pausa, il riepilogo rimane così com'è. Puoi comunque forzare un aggiornamento premendo il pulsante Riepiloga ora (disponibile solo con l'API principale).", "ext_sum_pause": "Pausa", "Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.": "Ometti World Info e Author's Note dal testo da riassumere. Ha effetto solo quando si usa l'API principale. L'API Extras omette sempre WI/AN.", "ext_sum_no_wi_an": "Nessuna connessione WI/AN", "ext_sum_settings_tip": "Modifica la richiesta di riepilogo, la posizione di inserimento, ecc.", "ext_sum_settings": "Impostazioni di riepilogo", "ext_sum_prompt_builder": "Costruttore rapido", "ext_sum_prompt_builder_1_desc": "L'estensione creerà il proprio prompt utilizzando i messaggi che non sono stati ancora riepilogati. Blocca la chat finché non viene generato il riepilogo.", "ext_sum_prompt_builder_1": "Crudo, bloccante", "ext_sum_prompt_builder_2_desc": "L'estensione creerà il proprio prompt utilizzando i messaggi che non sono stati ancora riepilogati. Non blocca la chat durante la generazione del riepilogo. Non tutti i backend supportano questa modalità.", "ext_sum_prompt_builder_2": "Grezzo, non bloccante", "ext_sum_prompt_builder_3_desc": "L'estensione utilizzerà il normale generatore di prompt principale e vi aggiungerà la richiesta di riepilogo come ultimo messaggio di sistema.", "ext_sum_prompt_builder_3": "Classico, bloccante", "Summary Prompt": "Richiesta di riepilogo", "ext_sum_restore_default_prompt_tip": "<PERSON><PERSON><PERSON><PERSON> richiesta predefinita", "ext_sum_prompt_placeholder": "Questa richiesta verrà inviata ad AI per richiedere la generazione del riepilogo. {{words}} verrà risolto nel parametro \"Numero di parole\".", "ext_sum_target_length_1": "Lunghezza del riepilogo target", "ext_sum_target_length_2": null, "ext_sum_target_length_3": "parole)", "ext_sum_api_response_length_1": "Lunghezza della risposta API", "ext_sum_api_response_length_2": null, "ext_sum_api_response_length_3": "gettoni)", "ext_sum_0_default": "0 = predefinito", "ext_sum_raw_max_msg": "[Raw] Numero massimo di messaggi per richiesta", "ext_sum_0_unlimited": "0 = illimitato", "Update frequency": "Frequenza di aggiornamento", "ext_sum_update_every_messages_1": "Aggiorna ogni", "ext_sum_update_every_messages_2": "<PERSON><PERSON><PERSON>", "ext_sum_0_disable": "0 = disabilita", "ext_sum_auto_adjust_desc": "Prova a regolare automaticamente l'intervallo in base alle metriche della chat.", "ext_sum_update_every_words_1": "Aggiorna ogni", "ext_sum_update_every_words_2": "parole", "ext_sum_both_sliders": "Se entrambi i dispositivi di scorrimento sono diversi da zero, entrambi attiveranno gli aggiornamenti di riepilogo ai rispettivi intervalli.", "ext_sum_injection_template": "Modello di iniezione", "ext_sum_memory_template_placeholder": "{{summary}} verrà risolto con il contenuto del riepilogo corrente.", "ext_sum_injection_position": "Posizione di iniezione", "How many messages before the current end of the chat.": "Quanti messaggi prima della fine corrente della chat.", "ext_regex_title": "Regex", "ext_regex_new_global_script": "+ Globale", "ext_regex_new_scoped_script": "+ Ambito", "ext_regex_import_script": "Importare", "ext_regex_global_scripts": "Script globali", "ext_regex_global_scripts_desc": "Disponibile per tutti i personaggi. Salvato nelle impostazioni locali.", "ext_regex_scoped_scripts": "Script con ambito", "ext_regex_scoped_scripts_desc": "Disponibile solo per questo personaggio. Salvato nei dati della carta.", "Regex Editor": "Editor di espressioni regolari", "Test Mode": "Modalità di prova", "ext_regex_desc": "Regex è uno strumento per trovare/sostituire stringhe utilizzando espressioni regolari. Se vuoi saperne di più clicca sul ? accanto al titolo.", "Input": "Ingresso", "ext_regex_test_input_placeholder": "Digitare qui...", "Output": "Produzione", "ext_regex_output_placeholder": "<PERSON><PERSON><PERSON>", "Script Name": "Nome dello script", "Find Regex": "Trova Regex", "Replace With": "Sostituirlo con", "ext_regex_replace_string_placeholder": "Utilizza {{match}} per includere il testo corrispondente da Trova Regex o $1, $2, ecc. per i gruppi di acquisizione.", "Trim Out": "Tagliare", "ext_regex_trim_placeholder": "Taglia globalmente tutte le parti indesiderate da una corrispondenza regex prima della sostituzione. Separa ogni elemento con un invio.", "ext_regex_affects": "<PERSON><PERSON><PERSON>", "ext_regex_user_input": "Ingresso dell'utente", "ext_regex_ai_output": "Uscita AI", "Slash Commands": "<PERSON><PERSON><PERSON> barra", "ext_regex_min_depth_desc": "Se applicato ai prompt o alla visualizzazione, influisce solo sui messaggi con una profondità di almeno N livelli. 0 = ultimo messaggio, 1 = penultimo messaggio, ecc. Conta solo le voci WI @Depth e i messaggi utilizzabili, ovvero non nascosti o di sistema.", "Min Depth": "Profondità minima", "ext_regex_min_depth_placeholder": "Illimitato", "ext_regex_max_depth_desc": "Se applicato a prompt o display, influisce solo sui messaggi con una profondità non superiore a N livelli. 0 = ultimo messaggio, 1 = penultimo messaggio, ecc. Conta solo le voci WI @Depth e i messaggi utilizzabili, ovvero non nascosti o di sistema.", "ext_regex_other_options": "Altre opzioni", "Only Format Display": "Solo visualizzazione del formato", "ext_regex_only_format_prompt_desc": "La cronologia della chat non cambierà, solo il messaggio richiesto quando viene inviata la richiesta (al momento della generazione).", "Only Format Prompt (?)": "Solo richiesta di formattazione", "Run On Edit": "Esegui in fase di modifica", "ext_regex_substitute_regex_desc": "Sostituisci {{macros}} in Trova Regex prima di eseguirlo", "Substitute Regex": "Sostituisci Regex", "ext_regex_import_target": "Importa in:", "ext_regex_disable_script": "Disabilita script", "ext_regex_enable_script": "Abilita script", "ext_regex_edit_script": "Modifica copione", "ext_regex_move_to_global": "Passa agli script globali", "ext_regex_move_to_scoped": "Passare agli script con ambito", "ext_regex_export_script": "Esporta script", "ext_regex_delete_script": "Elimina copione", "Trigger Stable Diffusion": "Attiva la diffusione stabile", "sd_Yourself": "<PERSON> stesso", "sd_Your_Face": "La tua faccia", "sd_Me": "Me", "sd_The_Whole_Story": "L'intera storia", "sd_The_Last_Message": "L'ultimo messaggio", "sd_Raw_Last_Message": "<PERSON><PERSON><PERSON> mess<PERSON>gio grezzo", "sd_Background": "Sfondo", "Image Generation": "Generazione di immagini", "sd_refine_mode": "Consenti di modificare manualmente i prompt prima di inviarli all'API di generazione", "sd_refine_mode_txt": "Modifica i prompt prima della generazione", "sd_interactive_mode": "Genera automaticamente immagini quando invii messaggi come \"inviami una foto di gatto\".", "sd_interactive_mode_txt": "Modalità interattiva", "sd_multimodal_captioning": "Utilizza i sottotitoli multimodali per generare richieste per i ritratti di utenti e personaggi in base ai loro avatar.", "sd_multimodal_captioning_txt": "Utilizza didascalie multimodali per i ritratti", "sd_expand": "Estendi automaticamente i prompt utilizzando il modello di generazione del testo", "sd_expand_txt": "Prompt di miglioramento automatico", "sd_snap": "Richieste di generazione di snap con proporzioni forzate (ritratti, sfondi) alla risoluzione conosciuta più vicina, cercando di preservare il numero assoluto di pixel (consigliato per SDXL).", "sd_snap_txt": "Risoluzioni autoregolate Snap", "Source": "Fonte", "sd_auto_url": "Esempio: {{auto_url}}", "Authentication (optional)": "Autenticazione (facoltativa)", "Example: username:password": "Esempio: nome utente: password", "Important:": "Importante:", "sd_auto_auth_warning_1": "eseguire l'interfaccia utente Web SD con", "sd_auto_auth_warning_2": "bandiera! Il server deve essere accessibile dalla macchina host SillyTavern.", "sd_drawthings_url": "Esempio: {{drawthings_url}}", "sd_drawthings_auth_txt": "esegui l'app DrawThings con lo switch API HTTP abilitato nell'interfaccia utente! Il server deve essere accessibile dalla macchina host SillyTavern.", "sd_vlad_url": "Esempio: {{vlad_url}}", "The server must be accessible from the SillyTavern host machine.": "Il server deve essere accessibile dalla macchina host SillyTavern.", "Hint: Save an API key in AI Horde API settings to use it here.": "Suggerimento: salva una chiave API nelle impostazioni API AI Horde per usarla qui.", "Allow NSFW images from Horde": "Consenti immagini NSFW da Horde", "Sanitize prompts (recommended)": "Messaggi di disinfezione (consigliato)", "Automatically adjust generation parameters to ensure free image generations.": "Regola automaticamente i parametri di generazione per garantire generazioni di immagini gratuite.", "Avoid spending Anlas": "Evita di spendere Anlas", "Opus tier": "(Livello Opus)", "View my Anlas": "Visualizza il mio Anlas", "These settings only apply to DALL-E 3": "Queste impostazioni si applicano solo a DALL-E 3", "Image Style": "Stile immagine", "Image Quality": "Qualità dell'immagine", "Standard": "Standard", "HD": "HD", "sd_comfy_url": "Esempio: {{comfy_url}}", "Open workflow editor": "Apri l'editor del flusso di lavoro", "Create new workflow": "Crea un nuovo flusso di lavoro", "Delete workflow": "Elimina flusso di lavoro", "Enhance": "<PERSON><PERSON><PERSON><PERSON>", "Refine": "Perfeziona", "Decrisper": "<PERSON><PERSON><PERSON>", "Sampling steps": "Passaggi di campionamento ()", "Width": "Larg<PERSON><PERSON> ()", "Height": "Altezza ()", "Resolution": "Risoluzione", "Model": "<PERSON><PERSON>", "Sampling method": "Metodo di campionamento", "Karras (not all samplers supported)": "<PERSON><PERSON><PERSON> (non tutti i campionatori sono supportati)", "SMEA versions of samplers are modified to perform better at high resolution.": "Le versioni SMEA dei campionatori sono state modificate per garantire prestazioni migliori ad alta risoluzione.", "SMEA": "SMEA", "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.": "Le varianti DYN dei campionatori SMEA spesso portano a un output più vario, ma potrebbero fallire a risoluzioni molto elevate.", "DYN": "DIN", "Scheduler": "Pianificatore", "Restore Faces": "<PERSON><PERSON><PERSON><PERSON>ti", "Hires. Fix": "Assunzioni. Aggiustare", "Upscaler": "Miglioratore", "Upscale by": "Di lusso da", "Denoising strength": "Forza di denoising", "Hires steps (2nd pass)": "Passaggi di assunzione (2° passaggio)", "Preset for prompt prefix and negative prompt": "Preimpostato per prefisso prompt e prompt negativo", "Style": "Stile", "Save style": "Salva stile", "Delete style": "<PERSON>mina stile", "Common prompt prefix": "Prefisso prompt comune", "sd_prompt_prefix_placeholder": "U<PERSON><PERSON><PERSON><PERSON> {prompt} per specificare dove verrà inserito il prompt generato", "Negative common prompt prefix": "Prefisso prompt comune negativo", "Character-specific prompt prefix": "Prefisso del prompt specifico del carattere", "Won't be used in groups.": "Non verrà utilizzato in gruppi.", "sd_character_prompt_placeholder": "Qualsiasi caratteristica che descriva il personaggio attualmente selezionato. Verrà aggiunto dopo un prefisso prompt comune.\nEsempio: donna, occhi verdi, capelli castani, maglietta rosa", "Character-specific negative prompt prefix": "Prefisso del prompt negativo specifico del carattere", "sd_character_negative_prompt_placeholder": "Qualsiasi caratteristica che non dovrebbe apparire per il personaggio selezionato. Verrà aggiunto dopo un prefisso prompt comune negativo.\nEsempio: g<PERSON><PERSON><PERSON>, scarpe, o<PERSON><PERSON>", "Shareable": "Condivisibile", "Image Prompt Templates": "Modelli di richiesta immagine", "Vectors Model Warning": "Si consiglia di eliminare i vettori quando si modifica il modello durante la chat. Altrimenti porterà a risultati inferiori alla media.", "Translate files into English before processing": "Traduci i file in inglese prima dell'elaborazione", "Manager Users": "<PERSON><PERSON><PERSON><PERSON> u<PERSON>ti", "New User": "Nuovo utente", "Status:": "Stato:", "Created:": "Creato:", "Display Name:": "<PERSON>me da visualizzare:", "User Handle:": "Maniglia utente:", "Password:": "Parola d'ordine:", "Confirm Password:": "Conferma password:", "This will create a new subfolder...": "<PERSON><PERSON>ò creerà una nuova sottocartella nella directory /data/ con l'handle dell'utente come nome della cartella.", "Current Password:": "Password attuale:", "New Password:": "Nuova password:", "Confirm New Password:": "Conferma la nuova password:", "Debug Warning": "Le funzioni di questa categoria sono riservate agli utenti esperti. Non fare clic su nulla se non sei sicuro delle conseguenze.", "Execute": "<PERSON><PERSON><PERSON><PERSON>", "Are you sure you want to delete this user?": "Sei sicuro di voler eliminare questo utente?", "Deleting:": "Eliminazione:", "Also wipe user data.": "Cancella anche i dati utente.", "Warning:": "Avvertimento:", "This action is irreversible.": "Questa azione è irreversibile.", "Type the user's handle below to confirm:": "Digita l'handle dell'utente qui sotto per confermare:", "Import Characters": "<PERSON><PERSON><PERSON>", "Enter the URL of the content to import": "Inserisci l'URL del contenuto da importare", "Supported sources:": "Fonti supportate:", "char_import_1": "<PERSON><PERSON><PERSON> (collegamento diretto o ID)", "char_import_example": "Esempio:", "char_import_2": "Lorebook di Chub (collegamento diretto o ID)", "char_import_3": "<PERSON><PERSON><PERSON> (collegamento diretto o UUID)", "char_import_4": "Carattere Pygmalion.chat (collegamento diretto o UUID)", "char_import_5": "Carattere AICharacterCard.com (Link diretto o ID)", "char_import_6": "Collegamento PNG diretto (fare riferimento a", "char_import_7": "per gli host consentiti)", "char_import_8": "<PERSON><PERSON><PERSON> (collegamento diretto)", "Supports importing multiple characters.": "Supporta l'importazione di più caratteri.", "Write each URL or ID into a new line.": "<PERSON><PERSON><PERSON> ogni URL o ID in una nuova riga.", "Export for character": "Esporta per carattere", "Export prompts for this character, including their order.": "L'esportazione richiede questo personaggio, incluso il suo ordine.", "Export all": "Esporta tutto", "Export all your prompts to a file": "Esporta tutte le tue richieste in un file", "Insert prompt": "<PERSON><PERSON><PERSON><PERSON> prompt", "Delete prompt": "Elimina prompt", "Import a prompt list": "Importa un elenco di prompt", "Export this prompt list": "Esporta questo elenco di prompt", "Reset current character": "<PERSON><PERSON><PERSON><PERSON> personaggio attuale", "New prompt": "Nuovo prompt", "Prompts": "<PERSON><PERSON>", "Total Tokens:": "Token totali:", "prompt_manager_tokens": "Gettoni", "Are you sure you want to reset your settings to factory defaults?": "Sei sicuro di voler ripristinare le impostazioni predefinite di fabbrica?", "Don't forget to save a snapshot of your settings before proceeding.": "Non dimenticare di salvare un'istantanea delle tue impostazioni prima di procedere.", "Settings Snapshots": "Impostazioni Istantanee", "Record a snapshot of your current settings.": "Registra un'istantanea delle tue impostazioni attuali.", "Make a Snapshot": "Fai un'istantanea", "Restore this snapshot": "<PERSON><PERSON><PERSON><PERSON> questa istant<PERSON>a", "Hi,": "CIAO,", "To enable multi-account features, restart the SillyTavern server with": "Per abilitare le funzionalità multi-account, riavvia il server SillyTavern con", "set to true in the config.yaml file.": "impostato su true nel file config.yaml.", "Account Info": "Informazioni sull'account", "To change your user avatar, use the buttons below or select a default persona in the Persona Management menu.": "Per cambiare il tuo avatar utente, usa i pulsanti qui sotto o seleziona un personaggio predefinito nel menu Gestione Persona.", "Set your custom avatar.": "Imposta il tuo avatar personalizzato.", "Remove your custom avatar.": "R<PERSON><PERSON>vi il tuo avatar personalizzato.", "Handle:": "Maniglia:", "This account is password protected.": "Questo account è protetto da password.", "This account is not password protected.": "Questo account non è protetto da password.", "Account Actions": "Azioni dell'account", "Change Password": "Cam<PERSON>re la <PERSON>", "Manage your settings snapshots.": "Gestisci le istantanee delle tue impostazioni.", "Download a complete backup of your user data.": "Scarica un backup completo dei tuoi dati utente.", "Download Backup": "<PERSON><PERSON><PERSON>", "Danger Zone": "Zona pericolosa", "Reset your settings to factory defaults.": "Ripristina le impostazioni ai valori predefiniti di fabbrica.", "Reset Settings": "Rip<PERSON><PERSON> le impostazioni", "Wipe all user data and reset your account to factory settings.": "Cancella tutti i dati utente e ripristina il tuo account alle impostazioni di fabbrica.", "Reset Everything": "<PERSON><PERSON><PERSON><PERSON> tutto", "Reset Code:": "Codice di ripristino:", "Want to update?": "Vuoi aggiornare?", "How to start chatting?": "Come iniziare a chattare?", "Click _space": "Clic", "and select a": " e seleziona un", "Chat API": " API di chat", "and pick a character.": "e scegli un personaggio.", "You can browse a list of bundled characters in the": "Puoi sfogliare un elenco di personaggi raggruppati nel file", "Download Extensions & Assets": "Scarica estensioni e risorse", "menu within": "menù all'interno", "Confused or lost?": "Confuso o perso?", "click these icons!": "clicca su queste icone!", "in the chat bar": " nella barra di chat", "SillyTavern Documentation Site": "SillyTavern Sito di documentazione", "Extras Installation Guide": "Guida all'installazione di extra", "Still have questions?": "Hai ancora domande?", "Join the SillyTavern Discord": "Unisciti al SillyTavern Discord", "Post a GitHub issue": "Pubblica un problema su GitHub", "Contact the developers": "Contatta gli svilup<PERSON>tori"}