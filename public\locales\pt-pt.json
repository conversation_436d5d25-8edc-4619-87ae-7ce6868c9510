{"Favorite": "<PERSON><PERSON><PERSON><PERSON>", "Tag": "Marcação", "Duplicate": "Dup<PERSON><PERSON>", "Persona": "<PERSON><PERSON><PERSON>", "Delete": "Excluir", "AI Response Configuration": "Configuração de resposta da IA", "AI Configuration panel will stay open": "O painel de configuração da IA permanecerá aberto", "clickslidertips": "Clique para inserir valores manualmente.", "MAD LAB MODE ON": "MODO MAD LAB ON", "Documentation on sampling parameters": "Documentação sobre parâmetros de amostragem", "kobldpresets": "Configurações predefinidas do Kobold", "guikoboldaisettings": "Configurações da interface do KoboldAI", "Update current preset": "Atualizar predefinição atual", "Save preset as": "<PERSON>var predefinição como", "Import preset": "Importar predefinição", "Export preset": "Exportar predefinição", "Restore current preset": "Restaurar a configuração atual", "Delete the preset": "Excluir a predefinição", "novelaipresets": "Configurações predefinidas do NovelAI", "Default": "Padrão", "openaipresets": "Configurações predefinidas do OpenAI", "Text Completion presets": "Configurações predefinidas de conclusão de texto", "AI Module": "Módulo de IA", "Changes the style of the generated text.": "Altera o estilo do texto gerado.", "No Module": "<PERSON><PERSON><PERSON> m<PERSON>", "Instruct": "Instruir", "Prose Augmenter": "Aumentador de prosa", "Text Adventure": "Aventura de texto", "response legth(tokens)": "Comprimento da resposta (tokens)", "Streaming": "Streaming", "Streaming_desc": "Exibir a resposta pouco a pouco conforme ela é gerada", "context size(tokens)": "<PERSON><PERSON><PERSON> (tokens)", "unlocked": "Desbloqueado", "Only enable this if your model supports context sizes greater than 8192 tokens": "Ative isso apenas se seu modelo suportar tamanhos de contexto maiores que 8192 tokens", "Max prompt cost:": "<PERSON>usto imediato m<PERSON>xi<PERSON>:", "Display the response bit by bit as it is generated.": "Exibir a resposta bit a bit conforme é gerada.", "When this is off, responses will be displayed all at once when they are complete.": "Quando isso estiver desligado, as respostas serão exibidas de uma vez quando estiverem completas.", "Temperature": "Temperatura", "rep.pen": "Pena de repetição", "Rep. Pen. Range.": "Faixa de Pen. Rep.", "Rep. Pen. Slope": "Inclinação da Pena de Rep.", "Rep. Pen. Freq.": "Freq. Pen. Rep.", "Rep. Pen. Presence": "Presença de Pen. Rep.", "TFS": "TFS-Sistema de Segurança", "Phrase Repetition Penalty": "Pena de Repetição de Frase", "Off": "Des<PERSON><PERSON>", "Very light": "<PERSON><PERSON> leve", "Light": "<PERSON><PERSON>", "Medium": "Médio", "Aggressive": "Agressivo", "Very aggressive": "Muito agressivo", "Unlocked Context Size": "Tamanho do Contexto Desbloqueado", "Unrestricted maximum value for the context slider": "Valor máximo irrestrito para o controle deslizante de contexto", "Context Size (tokens)": "<PERSON><PERSON><PERSON>texto (tokens)", "Max Response Length (tokens)": "Comprimento Máximo da Resposta (tokens)", "Multiple swipes per generation": "Vários furtos por geração", "Enable OpenAI completion streaming": "Ativar streaming de conclusão do OpenAI", "Frequency Penalty": "Pena de Frequência", "Presence Penalty": "Pena de Presença", "Count Penalty": "Contar penalidade", "Top K": "Top K", "Top P": "Top P", "Repetition Penalty": "Penalidade de repetição", "Min P": "<PERSON>", "Top A": "Top A", "Quick Prompts Edit": "Edição Rápida de Prompts", "Main": "Principal", "NSFW": "Não recomendado para menores de 18 anos", "Jailbreak": "Jailbreak", "Utility Prompts": "Prompts de Utilidade", "Impersonation prompt": "Prompt de Impersonação", "Restore default prompt": "Restaurar prompt padr<PERSON>", "Prompt that is used for Impersonation function": "Prompt usado para a função de Impersonação", "World Info Format Template": "Modelo de formato de informações mundiais", "Restore default format": "Restaurar formato padrão", "Wraps activated World Info entries before inserting into the prompt.": "Envolve entradas de informações do mundo ativadas antes de inseri-las no prompt.", "scenario_format_template_part_1": "<PERSON>ar", "scenario_format_template_part_2": "para marcar um local onde o conteúdo é inserido.", "Scenario Format Template": "Modelo de formato de cenário", "Personality Format Template": "Modelo de formato de personalidade", "Group Nudge Prompt Template": "Modelo de prompt de incentivo de grupo", "Sent at the end of the group chat history to force reply from a specific character.": "Enviado no final do histórico do chat em grupo para forçar a resposta de um personagem específico.", "New Chat": "Novo chat", "Restore new chat prompt": "Restaurar novo prompt de bate-papo", "Set at the beginning of the chat history to indicate that a new chat is about to start.": "Definido no início do histórico de bate-papo para indicar que um novo bate-papo está prestes a começar.", "New Group Chat": "Novo bate-papo em grupo", "Restore new group chat prompt": "Restaurar prompt padr<PERSON>", "Set at the beginning of the chat history to indicate that a new group chat is about to start.": "Definido no início do histórico de bate-papo para indicar que um novo bate-papo em grupo está prestes a começar.", "New Example Chat": "Novo exemplo de bate-papo", "Set at the beginning of Dialogue examples to indicate that a new example chat is about to start.": "Definido no início dos exemplos de Diálogo para indicar que um novo exemplo de bate-papo está prestes a começar.", "Continue nudge": "<PERSON><PERSON><PERSON><PERSON> cutucar", "Set at the end of the chat history when the continue button is pressed.": "Definido no final do histórico de bate-papo quando o botão continuar é pressionado.", "Replace empty message": "Substituir mensagem vazia", "Send this text instead of nothing when the text box is empty.": "Enviar este texto em vez de nada quando a caixa de texto estiver vazia.", "Seed": "Semente", "Set to get deterministic results. Use -1 for random seed.": "Definido para obter resultados determinísticos. Use -1 para sementes aleatórias.", "Temperature controls the randomness in token selection": "A temperatura controla a aleatoriedade na seleção de tokens", "Top_K_desc": "Top K define uma quantidade máxima de tokens principais que podem ser escolhidos", "Top_P_desc": "Top P (também conhecido como amostragem de núcleo)", "Typical P": "P Típico", "Typical_P_desc": "A amostragem típica de P prioriza tokens com base em sua divergência da entropia média do conjunto", "Min_P_desc": "Min P define uma probabilidade mínima base", "Top_A_desc": "Top A define um limiar para seleção de token com base no quadrado da maior probabilidade de token", "Tail_Free_Sampling_desc": "Amostragem sem cauda (TFS)", "rep.pen range": "Intervalo de pena de repetição", "Mirostat": "Mirostat", "Mode": "Modo", "Mirostat_Mode_desc": "Um valor 0 desativa totalmente o Mirostat. 1 é para Mirostat 1.0 e 2 é para Mirostat 2.0", "Tau": "Tau", "Mirostat_Tau_desc": "Controla a variabilidade das saídas do Mirostat", "Eta": "Eta", "Mirostat_Eta_desc": "Controla a taxa de aprendizagem do Mirostat", "Ban EOS Token": "Banir Token EOS", "Ban_EOS_Token_desc": "Banir o token End-of-Sequence (EOS) com KoboldCpp (e possivelmente também outros tokens com KoboldAI).\rBom para escrever histórias, mas não deve ser usado no modo chat e instrução.", "GBNF Grammar": "Gramática GBNF", "Type in the desired custom grammar": "Digite a gramática personalizada desejada", "Samplers Order": "Ordem dos samplers", "Samplers will be applied in a top-down order. Use with caution.": "Os samplers serão aplicados em ordem de cima para baixo. Use com cautela.", "Tail Free Sampling": "Amostragem Livre de Cauda", "Load koboldcpp order": "<PERSON><PERSON><PERSON> ordem kobold<PERSON>", "Preamble": "<PERSON><PERSON><PERSON><PERSON>", "Use style tags to modify the writing style of the output.": "Use tags de estilo para modificar o estilo de escrita da saída.", "Banned Tokens": "Tokens Proibidos", "Sequences you don't want to appear in the output. One per line.": "Sequências que você não quer que apareçam na saída. Uma por linha.", "Logit Bias": "<PERSON><PERSON><PERSON>", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Helps to ban or reenforce the usage of certain words": "A<PERSON><PERSON> a proibir ou reforçar o uso de certas palavras", "CFG Scale": "Escala CFG", "Negative Prompt": "Prompt Negativo", "Add text here that would make the AI generate things you don't want in your outputs.": "Adicione aqui texto que faria a IA gerar coisas que você não quer em suas saídas.", "Used if CFG Scale is unset globally, per chat or character": "Usado se a Escala CFG não estiver definida globalmente, por chat ou caractere", "Mirostat Tau": "Tau Mirostat", "Mirostat LR": "Mirostat LR", "Min Length": "Comprimento Mínimo", "Top K Sampling": "Amostragem dos Principais K", "Nucleus Sampling": "Amostragem de Núcleo", "Top A Sampling": "Amostragem dos Principais A", "CFG": "CFG", "Neutralize Samplers": "Neutralizar Amos<PERSON>dores", "Set all samplers to their neutral/disabled state.": "Defina todos os amostradores para seu estado neutro/desativado.", "Sampler Select": "Seleção de amostrador", "Customize displayed samplers or add custom samplers.": "Personalize os samplers exibidos ou adicione samplers personalizados.", "Epsilon Cutoff": "Limite Epsilon", "Epsilon cutoff sets a probability floor below which tokens are excluded from being sampled": "O corte de epsilon define um limite de probabilidade abaixo do qual os tokens são excluídos da amostragem", "Eta Cutoff": "Limite <PERSON>ta", "Eta_Cutoff_desc": "O corte Eta é o principal parâmetro da técnica especial de Amostragem Eta.&#13;Em unidades de 1e-4; um valor razoável é 3.&#13;Defina como 0 para desativar.&#13;Consulte o artigo Truncation Sampling as Language Model Desmoothing de <PERSON> et al. (2022) para mais detalhes.", "rep.pen decay": "Decadência da caneta representante", "Encoder Rep. Pen.": "Pena de Rep. do Codificador", "No Repeat Ngram Size": "<PERSON><PERSON><PERSON> de Ngrama sem repetição", "Skew": "Inclinar", "Max Tokens Second": "Máxi<PERSON> de Tokens por Segundo", "Smooth Sampling": "Amostragem Suave", "Smooth_Sampling_desc": "Permite usar transformações quadráticas/cúbicas para ajustar a distribuição. Valores mais baixos do fator de suavização serão mais criativos, geralmente entre 0,2-0,3 é o ponto ideal (assumindo que a curva = 1). Valores mais altos da curva de suavização tornarão a curva mais íngreme, o que punirá as escolhas de baixa probabilidade de forma mais agressiva. A curva 1.0 equivale a usar apenas o fator de suavização.", "Smoothing Factor": "<PERSON><PERSON>", "Smoothing Curve": "Curva de suavização", "DRY_Repetition_Penalty_desc": "DRY penaliza tokens que estenderiam o fim da entrada em uma sequência que ocorreu anteriormente na entrada. Defina o multiplicador como 0 para desabilitar.", "DRY Repetition Penalty": "Penalidade de Repetição SECA", "DRY_Multiplier_desc": "Defina para valor > 0 para ativar DRY. Controla a magnitude da penalidade para as sequências penalizadas mais curtas.", "Multiplier": "Multiplicador", "DRY_Base_desc": "Controla a rapidez com que a penalidade aumenta com o aumento do comprimento da sequência.", "Base": "Base", "DRY_Allowed_Length_desc": "A sequência mais longa que pode ser repetida sem ser penalizada.", "Allowed Length": "Comprimento permitido", "Penalty Range": "Faixa de Penalidade", "DRY_Sequence_Breakers_desc": "Tokens nos quais a correspondência de sequência não é continuada. Especificado como uma lista separada por vírgulas de cadeias de caracteres entre aspas.", "Sequence Breakers": "Quebradores de sequência", "JSON-serialized array of strings.": "Matriz de strings serializada em JSON.", "Dynamic Temperature": "Temperatura Dinâmica", "Scale Temperature dynamically per token, based on the variation of probabilities": "Escala de temperatura dinamicamente por token, com base na variação de probabilidades", "Minimum Temp": "Temperatura Mínima", "Maximum Temp": "Temperatura Máxima", "Exponent": "Expoente", "Mirostat (mode=1 is only for llama.cpp)": "Mirostat (modo=1 é apenas para llama.cpp)", "Mirostat_desc": "Mirostat é um termostato para perplexidade de saída", "Mirostat Mode": "Modo Mirostat", "Variability parameter for Mirostat outputs": "Parâmetro de variabilidade para saídas Mirostat", "Mirostat Eta": "Eta Mirostat", "Learning rate of Mirostat": "Taxa de aprendizado de Mirostat", "Beam search": "Busca de feixe", "Helpful tip coming soon.": "Dica útil em breve.", "Number of Beams": "Número de Feixes", "Length Penalty": "Pena de Comprimento", "Early Stopping": "Parada Antecipada", "Contrastive search": "Busca Contrastiva", "Penalty Alpha": "Alfa de Penalidade", "Strength of the Contrastive Search regularization term. Set to 0 to disable CS": "Força do termo de regularização de busca contrastante. Defina como 0 para desativar o CS.", "Do Sample": "Amostra", "Add BOS Token": "Ad<PERSON>onar <PERSON> BOS", "Add the bos_token to the beginning of prompts. Disabling this can make the replies more creative": "Adicione o token BOS ao início das prompts. <PERSON><PERSON><PERSON> isso pode tornar as respostas mais criativas", "Ban the eos_token. This forces the model to never end the generation prematurely": "Banir o token eos_token. Isso faz com que o modelo nunca termine a geração prematuramente", "Ignore EOS Token": "Ignorar token EOS", "Ignore the EOS Token even if it generates.": "Ignore o token EOS mesmo que ele seja gerado.", "Skip Special Tokens": "<PERSON>ular Tokens Especiais", "Temperature Last": "Temperatura Final", "Temperature_Last_desc": "Usar o amostrador de temperatura por último", "Speculative Ngram": "Ngram especulativo", "Use a different speculative decoding method without a draft model": "Use um método de decodificação especulativa diferente sem um modelo preliminar.\rÉ preferível usar um modelo de rascunho. O ngram especulativo não é tão eficaz.", "Spaces Between Special Tokens": "Espaços entre tokens especiais", "LLaMA / Mistral / Yi models only": "Apenas modelos LLaMA / Mistral / Yi", "Example: some text [42, 69, 1337]": "Exemplo: algum texto [42, 69, 1337]", "Classifier Free Guidance. More helpful tip coming soon": "Orientação sem classificador. Mais dicas úteis em breve", "Scale": "Escala", "JSON Schema": "Esquema JSON", "Type in the desired JSON schema": "Digite o esquema JSON desejado", "Grammar String": "Cadeia de Gramática", "GBNF or EBNF, depends on the backend in use. If you're using this you should know which.": "GBNF ou EBNF, depende do backend em uso. Se você estiver usando isso, você deve saber qual.", "Top P & Min P": "P superior e P mínimo", "Load default order": "<PERSON>egar ordem padr<PERSON>", "llama.cpp only. Determines the order of samplers. If Mirostat mode is not 0, sampler order is ignored.": "apenas lhama.cpp. Determina a ordem dos amostradores. Se o modo Mirostat não for 0, a ordem do amostrador será ignorada.", "Sampler Priority": "Prioridade do amostrador", "Ooba only. Determines the order of samplers.": "Apenas Ooba. Determina a ordem dos amostradores.", "Character Names Behavior": "Comportamento dos nomes dos personagens", "Helps the model to associate messages with characters.": "<PERSON><PERSON><PERSON> o modelo a associar mensagens a personagens.", "None": "<PERSON><PERSON><PERSON>", "character_names_default": "Exceto para grupos e personas passadas. <PERSON>aso contr<PERSON>, certifique-se de fornecer nomes no prompt.", "Don't add character names.": "Não adicione nomes de personagens.", "Completion": "Objet<PERSON> de conclusão", "character_names_completion": "Aplicam-se restrições: apenas alfanuméricos latinos e sublinhados. Não funciona para todas as fontes, nomeadamente: Claude, MistralAI, Google.", "Add character names to completion objects.": "Adicione nomes de personagens a objetos de conclusão.", "Message Content": "<PERSON><PERSON><PERSON><PERSON> da mensagem", "Prepend character names to message contents.": "Anexe nomes de caracteres ao conteúdo da mensagem.", "Continue Postfix": "<PERSON><PERSON><PERSON><PERSON>", "The next chunk of the continued message will be appended using this as a separator.": "O próximo pedaço da mensagem continuada será anexado usando-o como separador.", "Space": "Espaço", "Newline": "Nova linha", "Double Newline": "Nova linha dupla", "Wrap user messages in quotes before sending": "Envolver mensagens do usuário entre aspas antes de enviar", "Wrap in Quotes": "Envolver em Aspas", "Wrap entire user message in quotes before sending.": "Envolver toda a mensagem do usuário em aspas antes de enviar.", "Leave off if you use quotes manually for speech.": "Deixe desativado se você usar aspas manualmente para fala.", "Continue prefill": "Continuar com preenchimento prévio", "Continue sends the last message as assistant role instead of system message with instruction.": "Continuar envia a última mensagem como papel de assistente em vez de mensagem do sistema com instrução.", "Squash system messages": "Agrupar mensagens do sistema", "Combines consecutive system messages into one (excluding example dialogues). May improve coherence for some models.": "Combina mensagens do sistema consecutivas em uma (excluindo diálogos de exemplo). Pode melhorar a coerência para alguns modelos.", "Enable function calling": "Habilitar chamada de função", "Send inline images": "Enviar imagens inline", "image_inlining_hint_1": "Envia imagens em prompts se o modelo suportar (por exemplo, GPT-4V, Claude 3 ou Llava 13B).\n                                                Use o", "image_inlining_hint_2": "ação em qualquer mensagem ou", "image_inlining_hint_3": "menu para anexar um arquivo de imagem ao chat.", "Inline Image Quality": "Qualidade de imagem embutida", "openai_inline_image_quality_auto": "Auto", "openai_inline_image_quality_low": "Baixo", "openai_inline_image_quality_high": "Alto", "Use AI21 Tokenizer": "Use o tokenizador AI21", "Use the appropriate tokenizer for Jurassic models, which is more efficient than GPT's.": "Use o tokenizer apropriado para modelos Jurássicos, que é mais eficiente que o GPT.", "Use Google Tokenizer": "Usar Tokenizer do Google", "Use the appropriate tokenizer for Google models via their API. Slower prompt processing, but offers much more accurate token counting.": "Use o tokenizador apropriado para modelos do Google via sua API. Processamento de prompt mais lento, mas oferece contagem de token muito mais precisa.", "Use system prompt": "Usar prompt do sistema", "(Gemini 1.5 Pro/Flash only)": "(Somente Gemini 1.5 Pro/Flash)", "Merges_all_system_messages_desc_1": "<PERSON><PERSON><PERSON><PERSON> to<PERSON> as mensagens do sistema até a primeira mensagem com uma função que não seja do sistema e as envia em um", "Merges_all_system_messages_desc_2": "campo.", "Assistant Prefill": "Preenchimento prévio do assistente", "Start Claude's answer with...": "Iniciar respost<PERSON> com...", "Assistant Impersonation Prefill": "Pré-preenchimento de representação do assistente", "Use system prompt (Claude 2.1+ only)": "Usar prompt do sistema (apenas Claude 2.1+)", "Send the system prompt for supported models. If disabled, the user message is added to the beginning of the prompt.": "Enviar o prompt do sistema para modelos suportados. Se desativado, a mensagem do usuário é adicionada ao início do prompt.", "User first message": "Primeira mensagem do usuário", "Restore User first message": "Restaurar a primeira mensagem do usuário", "Human message": "Mensagem humana, instrução, etc.\nNão adiciona nada quando vazio, ou seja, requer um novo prompt com a função 'usuário'.", "New preset": "Nova predefinição", "Delete preset": "Excluir predefinição", "View / Edit bias preset": "Ver / Editar predefinição de viés", "Add bias entry": "Adicionar entrada de viés", "Most tokens have a leading space.": "A maioria dos tokens tem um espaço à esquerda.", "API Connections": "Conexões da <PERSON>", "Text Completion": "Conclusão de texto", "Chat Completion": "Conclusão do bate-papo", "NovelAI": "NovelAI", "AI Horde": "AI Horde", "KoboldAI": "KoboldAI", "Avoid sending sensitive information to the Horde.": "Evite enviar informações sensíveis para a Horda.", "Review the Privacy statement": "Reveja a declaração de privacidade", "Register a Horde account for faster queue times": "Registre uma conta Horde para tempos de fila mais rápidos", "Learn how to contribute your idle GPU cycles to the Horde": "Saiba como contribuir com seus ciclos de GPU inativos para a Horda", "Adjust context size to worker capabilities": "Ajustar o tamanho do contexto às capacidades do trabalhador", "Adjust response length to worker capabilities": "Ajustar o comprimento da resposta às capacidades do trabalhador", "Can help with bad responses by queueing only the approved workers. May slowdown the response time.": "Pode ajudar com respostas ruins, enfileirando apenas os trabalhadores aprovados. Pode diminuir a velocidade de resposta.", "Trusted workers only": "Apenas trabalhadores confiáveis", "API key": "<PERSON><PERSON> da <PERSON>", "Get it here:": "Obt<PERSON><PERSON> aqui:", "Register": "Registrar", "View my Kudos": "Ver meus Kudos", "Enter": "Entrar", "to use anonymous mode.": "para usar o modo anônimo.", "Clear your API key": "Limpar sua chave da API", "For privacy reasons, your API key will be hidden after you reload the page.": "Por motivos de privacidade, sua chave de API será ocultada depois que você recarregar a página.", "Models": "Modelos", "Refresh models": "Atualizar modelos", "-- Horde models not loaded --": "-- <PERSON><PERSON> da Horda não carregados --", "Not connected...": "Não conectado...", "API url": "URL da API", "Example: http://127.0.0.1:5000/api ": "Exemplo: http://127.0.0.1:5000/api", "Connect": "Conectar", "Cancel": "<PERSON><PERSON><PERSON>", "Novel API key": "<PERSON>ve da <PERSON> do Novell", "Get your NovelAI API Key": "Obtenha sua chave de API NovelAI", "Enter it in the box below": "Digite-o na caixa abaixo", "Novel AI Model": "Modelo Novel AI", "No connection...": "<PERSON><PERSON> conex<PERSON>...", "API Type": "Tipo de API", "Default (completions compatible)": "Padrão [compatível com OpenAI /conclusões: oobabooga, LM Studio, etc.]", "TogetherAI API Key": "Chave da API TogetherAI", "TogetherAI Model": "Modelo TogetherAI", "-- Connect to the API --": "-- Conectar-se à API --", "OpenRouter API Key": "<PERSON><PERSON> da <PERSON> OpenRouter", "Click Authorize below or get the key from": "Clique em Autorizar abaixo ou obtenha a chave de", "View Remaining Credits": "Ver créditos restantes", "OpenRouter Model": "<PERSON><PERSON> OpenRouter", "Model Providers": "Provedores de modelos", "InfermaticAI API Key": "Chave API InfermaticAI", "InfermaticAI Model": "Modelo InfermaticAI", "DreamGen API key": "<PERSON><PERSON> da <PERSON> DreamGen", "DreamGen Model": "<PERSON><PERSON> DreamGen", "Mancer API key": "<PERSON><PERSON> da <PERSON>", "Mancer Model": "<PERSON><PERSON>", "Make sure you run it with": "Certifique-se de executá-lo com", "flag": "bandeira", "API key (optional)": "<PERSON><PERSON> da <PERSON> (opcional)", "Server url": "URL do servidor", "Example: 127.0.0.1:5000": "Exemplo: 127.0.0.1:5000", "Custom model (optional)": "Modelo personalizado (opcional)", "vllm-project/vllm": "vllm-project/vllm (modo wrapper da API OpenAI)", "vLLM API key": "Chave de API vLLM", "Example: 127.0.0.1:8000": "Exemplo: http://127.0.0.1:8000", "vLLM Model": "Modelo vLLM", "PygmalionAI/aphrodite-engine": "PygmalionAI/aphrodite-engine (Modo Wrapper para API OpenAI)", "Aphrodite API key": "<PERSON>ve da <PERSON> Aphrodite", "Aphrodite Model": "<PERSON><PERSON>", "ggerganov/llama.cpp": "ggerganov/llama.cpp (Servidor de Saída)", "Example: 127.0.0.1:8080": "Exemplo: 127.0.0.1:8080", "Example: 127.0.0.1:11434": "Exemplo: 127.0.0.1:11434", "Ollama Model": "<PERSON><PERSON>", "Download": "Baixar", "Tabby API key": "Chave da <PERSON> do <PERSON>", "koboldcpp API key (optional)": "Chave API koboldcpp (opcional)", "Example: 127.0.0.1:5001": "Exemplo: 127.0.0.1:5001", "Authorize": "Autorizar", "Get your OpenRouter API token using OAuth flow. You will be redirected to openrouter.ai": "Obtenha seu token da API do OpenRouter usando o fluxo OAuth. Você será redirecionado para openrouter.ai", "Bypass status check": "Ignorar verificação de status", "Chat Completion Source": "Fonte de conclusão de chat", "Reverse Proxy": "Proxy reverso", "Proxy Presets": "Predefinições de proxy", "Saved addresses and passwords.": "Endereços e senhas salvos.", "Save Proxy": "<PERSON>var Proxy", "Delete Proxy": "Excluir proxy", "Proxy Name": "Nome do proxy", "This will show up as your saved preset.": "Isso aparecerá como sua predefinição salva.", "Proxy Server URL": "URL do servidor proxy", "Alternative server URL (leave empty to use the default value).": "URL do servidor alternativo (deixe em branco para usar o valor padrão).", "Remove your real OAI API Key from the API panel BEFORE typing anything into this box": "Remova sua verdadeira chave de API OAI do painel de API ANTES de digitar algo nesta caixa", "We cannot provide support for problems encountered while using an unofficial OpenAI proxy": "Não podemos fornecer suporte para problemas encontrados ao usar um proxy não oficial da OpenAI", "Doesn't work? Try adding": "Não funciona? Tente adicionar", "at the end!": "no final!", "Proxy Password": "Senha do proxy", "Will be used as a password for the proxy instead of API key.": "Será usado como senha do proxy em vez da chave API.", "Peek a password": "<PERSON><PERSON><PERSON> uma senha", "OpenAI API key": "Chave da API OpenAI", "View API Usage Metrics": "Ver métricas de uso da API", "Follow": "<PERSON><PERSON><PERSON>", "these directions": "estas direções", "to get your OpenAI API key.": "para obter sua chave da API do OpenAI.", "Use Proxy password field instead. This input will be ignored.": "Use o campo \"Senha do proxy\". Esta entrada será ignorada.", "OpenAI Model": "Modelo OpenAI", "Bypass API status check": "Ignorar verificação de status da API", "Show External models (provided by API)": "Mostrar modelos externos (fornecidos pela API)", "Get your key from": "Obtenha sua chave de", "Anthropic's developer console": "console de desenvolved<PERSON> Anthrop<PERSON>", "Claude Model": "<PERSON><PERSON>", "Window AI Model": "Modelo Window AI", "Model Order": "Classificação de modelo OpenRouter", "Alphabetically": "Alfabeticamente", "Price": "Preço (mais barato)", "Context Size": "Tamanho do <PERSON>o", "Group by vendors": "Agrupar por fornecedores", "Group by vendors Description": "Coloque os modelos OpenAI em um grupo, os modelos antrópicos em outro grupo, etc.", "Allow fallback routes": "<PERSON><PERSON><PERSON> rotas de fallback", "Allow fallback routes Description": "O modelo alternativo será escolhido automaticamente se o modelo selecionado não puder atender à sua solicitação.", "Scale API Key": "Chave da <PERSON> Scale", "Clear your cookie": "Limpe seu cookie", "Alt Method": "Mé<PERSON>do <PERSON>", "AI21 API Key": "<PERSON>ve da API AI21", "AI21 Model": "Modelo AI21", "Google AI Studio API Key": "Chave API Google AI Studio", "Google Model": "Modelo Google", "MistralAI API Key": "Chave de API MistralAI", "MistralAI Model": "Modelo MistralAI", "Groq API Key": "Chave API Groq", "Groq Model": "<PERSON><PERSON>", "Perplexity API Key": "Chave de API de perplexidade", "Perplexity Model": "Modelo de Perplexidade", "Cohere API Key": "<PERSON>ve da <PERSON>", "Cohere Model": "<PERSON><PERSON>", "Custom Endpoint (Base URL)": "Endpoint personalizado (URL base)", "Custom API Key": "Chave de API personalizada", "Available Models": "Modelos Disponíveis", "Prompt Post-Processing": "Pós-processamento imediato", "Applies additional processing to the prompt before sending it to the API.": "Aplica processamento adicional ao prompt antes de enviá-lo para a API.", "Verifies your API connection by sending a short test message. Be aware that you'll be credited for it!": "Verifica sua conexão com a API enviando uma mensagem de teste curta. Esteja ciente de que você será creditado por isso!", "Test Message": "Mensagem de Teste", "Auto-connect to Last Server": "Conectar-se automaticamente ao último servidor", "Missing key": "❌ Chave faltando", "Key saved": "✔️ Chave salva", "View hidden API keys": "Ver chaves de API ocultas", "AI Response Formatting": "Formatação de resposta da IA", "Advanced Formatting": "Formatação Avançada", "Context Template": "<PERSON>o de Contexto", "Auto-select this preset for Instruct Mode": "Selecionar automaticamente esta predefinição para o Modo de Instrução", "Story String": "String de História", "Example Separator": "Separador de Exemplo", "Chat Start": "Início do Chat", "Add Chat Start and Example Separator to a list of stopping strings.": "Adicione o início do bate-papo e o separador de exemplo a uma lista de strings de parada.", "Use as Stop Strings": "Usar como Strings de Parada", "context_allow_jailbreak": "Inclui Jailbreak no final do prompt, se definido no cartão de personagem E ''Prefer Char. Jailbreak'' está habilitado.\nISTO NÃO É RECOMENDADO PARA MODELOS DE COMPLEMENTAÇÃO DE TEXTO, PODE LEVAR A UMA SAÍDA RUIM.", "Allow Jailbreak": "<PERSON><PERSON><PERSON> jailbreak", "Context Order": "Ordem de Contexto", "Summary": "Resumo", "Author's Note": "Nota do autor", "Example Dialogues": "Diálogos de exemplo", "Hint": "Dica:", "In-Chat Position not affected": "Os pedidos de Resumo e Nota do Autor só são afetados quando não possuem uma posição no Chat definida.", "Instruct Mode": "Modo de Instrução", "Enabled": "<PERSON><PERSON>do", "instruct_bind_to_context": "Se ativado, os modelos de contexto serão selecionados automaticamente com base no nome do modelo de instrução selecionado ou por preferência.", "Bind to Context": "Vincular ao Contexto", "Presets": "Predefinições", "Auto-select this preset on API connection": "Selecionar automaticamente esta predefinição na conexão da API", "Activation Regex": "Regex de Ativação", "Wrap Sequences with Newline": "Envolver Sequências com Nova Linha", "Replace Macro in Sequences": "Substituir Macro em Sequências", "Skip Example Dialogues Formatting": "Pular formatação de diálogos de exemplo", "Include Names": "Incluir No<PERSON>", "Force for Groups and Personas": "Forçar para Grupos e Personas", "System Prompt": "Prompt do Sistema", "Instruct Mode Sequences": "Sequências de Modo de Instrução", "System Prompt Wrapping": "Envolvimento de prompt do sistema", "Inserted before a System prompt.": "Inserido antes de um prompt do sistema.", "System Prompt Prefix": "Prefixo do prompt do sistema", "Inserted after a System prompt.": "Inserido após um prompt do sistema.", "System Prompt Suffix": "Sufixo de prompt do sistema", "Chat Messages Wrapping": "Encerramento de mensagens de bate-papo", "Inserted before a User message and as a last prompt line when impersonating.": "Inserido antes de uma mensagem do usuário e como última linha de prompt ao representar.", "User Message Prefix": "Prefixo da mensagem do usuário", "Inserted after a User message.": "Inserido após uma mensagem do usuário.", "User Message Suffix": "Sufixo da mensagem do usuário", "Inserted before an Assistant message and as a last prompt line when generating an AI reply.": "Inserido antes de uma mensagem do Assistente e como última linha de prompt ao gerar uma resposta de IA.", "Assistant Message Prefix": "Prefixo da mensagem do assistente", "Inserted after an Assistant message.": "Inserido após uma mensagem do Assistente.", "Assistant Message Suffix": "Sufixo de mensagem do assistente", "Inserted before a System (added by slash commands or extensions) message.": "Inserido antes de uma mensagem do Sistema (adicionada por comandos de barra ou extensões).", "System Message Prefix": "Prefixo de mensagem do sistema", "Inserted after a System message.": "Inserido após uma mensagem do sistema.", "System Message Suffix": "Sufixo de mensagem do sistema", "If enabled, System Sequences will be the same as User Sequences.": "Se ativado, as sequências do sistema serão iguais às sequências do usuário.", "System same as User": "Sistema igual ao Usuário", "Misc. Sequences": "Diversos. Sequências", "Inserted before the first Assistant's message.": "Inserido antes da primeira mensagem do Assistente.", "First Assistant Prefix": "Prefixo do primeiro assistente", "instruct_last_output_sequence": "Inserido antes da última mensagem do Assistente ou como última linha de prompt ao gerar uma resposta de IA (exceto uma função neutra/de sistema).", "Last Assistant Prefix": "Último prefixo do assistente", "Will be inserted as a last prompt line when using system/neutral generation.": "Será inserido como última linha de prompt ao usar geração de sistema/neutro.", "System Instruction Prefix": "Prefixo de instrução do sistema", "If a stop sequence is generated, everything past it will be removed from the output (inclusive).": "Se uma sequência de parada for gerada, tudo que passar dela será removido da saída (inclusive).", "Stop Sequence": "Sequência de Parada", "Will be inserted at the start of the chat history if it doesn't start with a User message.": "Será inserido no início do histórico do chat se não começar com uma mensagem do usuário.", "User Filler Message": "Mensagem de preenchimento do usuário", "Context Formatting": "Formatação de Contexto", "(Saved to Context Template)": "(Salvo no Modelo de Contexto)", "Always add character's name to prompt": "Sempre adicionar nome do personagem à prompt", "Generate only one line per request": "<PERSON><PERSON><PERSON> apenas uma linha por solicitação", "Trim Incomplete Sentences": "Remover Frases Incompletas", "Include Newline": "Incluir Nova Linha", "Misc. Settings": "Configurações Diversas", "Collapse Consecutive Newlines": "Colapsar Nova Linha Consecutiva", "Trim spaces": "Remover espaços", "Tokenizer": "Tokenizador", "Token Padding": "Preenchimento de Token", "Start Reply With": "Iniciar <PERSON><PERSON><PERSON><PERSON>", "AI reply prefix": "Prefixo de resposta da IA", "Show reply prefix in chat": "Mostrar prefixo de resposta no chat", "Non-markdown strings": "<PERSON><PERSON> de caracteres não Markdown", "separate with commas w/o space between": "separe com vírgulas sem espaço entre", "Custom Stopping Strings": "Cadeias de parada personalizadas", "JSON serialized array of strings": "Matriz de strings serializada em JSON", "Replace Macro in Custom Stopping Strings": "Substituir Macro em Strings de Parada Personalizadas", "Auto-Continue": "Auto-Continuar", "Allow for Chat Completion APIs": "Permitir APIs de Completar Chat", "Target length (tokens)": "Comprimento alvo (tokens)", "World Info": "Informações do Mundo", "Locked = World Editor will stay open": "Trancado = Editor Mundi<PERSON> per<PERSON><PERSON>", "Worlds/Lorebooks": "Mundos/Livros de Histórias", "Active World(s) for all chats": "Mundo(s) ativo(s) para todos os chats", "-- World Info not found --": "-- Informações do Mundo não encontradas --", "Global World Info/Lorebook activation settings": "Configurações de ativação do Global World Info/Lorebook", "Click to expand": "Clique para expandir", "Scan Depth": "Profundidade de Digitalização", "Context %": "Contexto %", "Budget Cap": "Limite de Orçamento", "(0 = disabled)": "(0 = desativado)", "Scan chronologically until reached min entries or token budget.": "Faça a varredura cronologicamente até atingir o mínimo de entradas ou o orçamento de tokens.", "Min Activations": "Ativações mínimas", "Max Depth": "Profundidade máxima", "(0 = unlimited, use budget)": "(0 = ilimitado, usar orçamento)", "Insertion Strategy": "Estratégia de Inserção", "Sorted Evenly": "Ordenado Uniformemente", "Character Lore First": "<PERSON>re do Personagem Primeiro", "Global Lore First": "Lore Global Primeiro", "Entries can activate other entries by mentioning their keywords": "As entradas podem ativar outras entradas mencionando suas palavras-chave", "Recursive Scan": "Verificação Recursiva", "Lookup for the entry keys in the context will respect the case": "A busca pelas chaves de entrada no contexto respeitará a caixa", "Case Sensitive": "Sensível a Maiúsculas", "If the entry key consists of only one word, it would not be matched as part of other words": "Se a chave de entrada consistir apenas em uma palavra, ela não será correspondida como parte de outras palavras", "Match Whole Words": "Corresponder <PERSON><PERSON>", "Only the entries with the most number of key matches will be selected for Inclusion Group filtering": "Somente as entradas com o maior número de correspondências de chave serão selecionadas para filtragem do Grupo de Inclusão", "Use Group Scoring": "Usar pontuação de grupo", "Alert if your world info is greater than the allocated budget.": "Alerte se as informações do seu mundo forem maiores que o orçamento alocado.", "Alert On Overflow": "Alerta em Overflow", "New": "Novo", "or": "ou", "--- Pick to Edit ---": "--- <PERSON><PERSON><PERSON><PERSON> ---", "Rename World Info": "Renomear informações do mundo", "Open all Entries": "<PERSON><PERSON><PERSON> as entradas", "Close all Entries": "<PERSON><PERSON><PERSON> as entradas", "New Entry": "Nova entrada", "Fill empty Memo/Titles with Keywords": "Preencher Memo/Títulos vazios com palavras-chave", "Import World Info": "Importar informações do mundo", "Export World Info": "Exportar informações do mundo", "Duplicate World Info": "Duplicar informações do mundo", "Delete World Info": "Excluir informações do mundo", "Search...": "Pesquisar...", "Search": "Procurar", "Priority": "Prioridade", "Custom": "Personalizado", "Title A-Z": "Título A-Z", "Title Z-A": "<PERSON><PERSON><PERSON>lo <PERSON>", "Tokens ↗": "Tokens ↗", "Tokens ↘": "Tokens ↘", "Depth ↗": "Profundidade ↗", "Depth ↘": "Profundidade ↘", "Order ↗": "Ordem ↗", "Order ↘": "Ordem ↘", "UID ↗": "UID ↗", "UID ↘": "UID ↘", "Trigger% ↗": "Desencadear% ↗", "Trigger% ↘": "Desencadear% ↘", "Refresh": "<PERSON><PERSON><PERSON><PERSON>", "User Settings": "Configurações do Usuário", "Simple": "Simples", "Advanced": "Avançado", "UI Language": "Idioma da UI", "Account": "Conta", "Admin Panel": "Painel de administração", "Logout": "<PERSON><PERSON>", "Search Settings": "Configurações de Busca", "UI Theme": "Tema da interface do usuário", "Import a theme file": "Importar um arquivo de tema", "Export a theme file": "Exportar um arquivo de tema", "Delete a theme": "Excluir um tema", "Update a theme file": "Atualizar um arquivo de tema", "Save as a new theme": "<PERSON>var como um novo tema", "Avatar Style": "Estilo <PERSON>", "Circle": "<PERSON><PERSON><PERSON><PERSON>", "Square": "Quadrado", "Rectangle": "Re<PERSON><PERSON><PERSON><PERSON>", "Chat Style:": "Estilo <PERSON>:", "Flat": "Plano\nBolhas\nDocumento", "Bubbles": "Bolhas", "Document": "Documento", "Specify colors for your theme.": "Especifique as cores do seu tema.", "Theme Colors": "Cores do tema", "Main Text": "<PERSON><PERSON> Principal", "Italics Text": "Texto em Itálico", "Underlined Text": "Texto sublinhado", "Quote Text": "Texto de Citação", "Shadow Color": "<PERSON><PERSON> So<PERSON>ra", "Chat Background": "Fundo de Chat", "UI Background": "Fundo da Interface", "UI Border": "<PERSON><PERSON>", "User Message Blur Tint": "Tom de Desfoque de Mensagem do Usuário", "AI Message Blur Tint": "Tom <PERSON> Desfoque de Mensagem da IA", "Chat Width": "Largura do bate-papo", "Width of the main chat window in % of screen width": "Largura da janela principal do chat em % da largura da tela", "Font Scale": "Escala de <PERSON>", "Font size": "<PERSON><PERSON><PERSON>", "Blur Strength": "Força do Desfoque", "Blur strength on UI panels.": "Força de desfoque nos painéis da IU.", "Text Shadow Width": "Largura da Sombra do Texto", "Strength of the text shadows": "Força das sombras do texto", "Disables animations and transitions": "Desativa animações e transições", "Reduced Motion": "Movimento reduzido", "removes blur from window backgrounds": "remove o desfoque dos fundos das janelas", "No Blur Effect": "Sem Efeito de Desfoque", "Remove text shadow effect": "Remover efeito de sombra de texto", "No Text Shadows": "Sem Sombras de Texto", "Reduce chat height, and put a static sprite behind the chat window": "Reduzir a altura do chat e colocar um sprite estático atrás da janela do chat", "Waifu Mode": "<PERSON><PERSON>", "Always show the full list of the Message Actions context items for chat messages, instead of hiding them behind '...'": "Sempre mostrar a lista completa dos itens de contexto de Ações de Mensagem para mensagens de chat, em vez de escondê-los atrás de '...'", "Auto-Expand Message Actions": "Expansão Automática de Ações de Mensagem", "Alternative UI for numeric sampling parameters with fewer steps": "Interface alternativa para parâmetros de amostragem numérica com menos etapas", "Zen Sliders": "Controles <PERSON>", "Entirely unrestrict all numeric sampling parameters": "Desrestringir completamente todos os parâmetros de amostragem numérica", "Mad Lab Mode": "Modo Mad Lab", "Time the AI's message generation, and show the duration in the chat log": "Temporizar a geração de mensagens da IA e mostrar a duração no registro de chat", "Message Timer": "Temporizador de Mensagens", "Show a timestamp for each message in the chat log": "Mostrar um carimbo de data/hora para cada mensagem no registro de chat", "Chat Timestamps": "Carimbos de Data/Hora do Chat", "Show an icon for the API that generated the message": "Mostrar um ícone para a API que gerou a mensagem", "Model Icon": "Ícone do Modelo", "Show sequential message numbers in the chat log": "Mostrar números sequenciais de mensagem no registro de chat", "Message IDs": "IDs de Mensagem", "Hide avatars in chat messages.": "Oculte avatares em mensagens de bate-papo.", "Hide Chat Avatars": "Ocultar avatares de bate-papo", "Show the number of tokens in each message in the chat log": "Mostrar o número de tokens em cada mensagem no registro de chat", "Show Message Token Count": "Mostrar Contagem de Tokens da Mensagem", "Single-row message input area. Mobile only, no effect on PC": "Área de entrada de mensagem de uma única linha. <PERSON><PERSON><PERSON> mó<PERSON>, sem efeito no PC", "Compact Input Area (Mobile)": "Área de Entrada Compacta (Móvel)", "In the Character Management panel, show quick selection buttons for favorited characters": "No painel de Gerenciamento de Personagens, mostrar botões de seleção rápida para personagens favoritos", "Characters Hotswap": "Substituição de Personagens a Quente", "Enable magnification for zoomed avatar display.": "Ative a ampliação para exibição de avatar ampliada.", "Avatar Hover Magnification": "Ampliação de foco do avatar", "Enables a magnification effect on hover when you display the zoomed avatar after clicking an avatar's image in chat.": "Ativa um efeito de ampliação ao passar o mouse quando você exibe o avatar ampliado após clicar na imagem de um avatar no bate-papo.", "Show tagged character folders in the character list": "Mostrar pastas de personagens marcados na lista de personagens", "Tags as Folders": "Tags como Pastas", "Tags_as_Folders_desc": "Mudança recente: as tags devem ser marcadas como pastas no menu Gerenciamento de tags para aparecerem como tal. Clique aqui para trazê-lo à tona.", "Character Handling": "Tratamento de Personagem", "If set in the advanced character definitions, this field will be displayed in the characters list.": "Se definido nas definições avançadas de personagem, este campo será exibido na lista de personagens.", "Char List Subheader": "Subcabeçalho da lista de caracteres", "Character Version": "Versão do personagem", "Created by": "<PERSON><PERSON><PERSON> por", "Use fuzzy matching, and search characters in the list by all data fields, not just by a name substring": "Usar correspondência fuzzy e pesquisar personagens na lista por todos os campos de dados, não apenas por uma subcadeia de nome", "Advanced Character Search": "Pesquisa Avançada de Personagens", "If checked and the character card contains a prompt override (System Prompt), use that instead": "Se marcado e o cartão de personagem contiver uma substituição de prompt (Prompt do Sistema), use isso em vez disso", "Prefer Character Card Prompt": "Preferir Prompt do Cartão de Personagem", "If checked and the character card contains a jailbreak override (Post History Instruction), use that instead": "Se marcado e o cartão de personagem contiver uma substituição de jailbreak (Instrução de Histórico de Postagens), use isso em vez disso", "Prefer Character Card Jailbreak": "Preferir Jailbreak do Cartão de Personagem", "Avoid cropping and resizing imported character images. When off, crop/resize to 512x768": "Evite cortar e redimensionar imagens de personagens importados. Quando desativado, corte/redimensione para 512x768.", "Never resize avatars": "Nunca redimensionar avatares", "Show actual file names on the disk, in the characters list display only": "Mostrar nomes de arquivo reais no disco, apenas na exibição da lista de personagens", "Show avatar filenames": "Mostrar nomes de arquivo de avatar", "Prompt to import embedded card tags on character import. Otherwise embedded tags are ignored": "Solicitar a importação de tags de cartão incorporadas na importação de personagens. Caso contrário, as tags incorporadas são ignoradas", "Import Card Tags": "Importar Tags de Cartão", "Hide character definitions from the editor panel behind a spoiler button": "Ocultar definições de personagens do painel do editor atrás de um botão de spoiler", "Spoiler Free Mode": "<PERSON><PERSON>poilers", "Miscellaneous": "Diversos", "Reload and redraw the currently open chat": "Recarregar e redesenhar o chat atualmente aberto", "Reload Chat": "<PERSON><PERSON><PERSON><PERSON>", "Debug Menu": "Menu de Depuração", "Smooth Streaming": "Transmissão Suave", "Experimental feature. May not work for all backends.": "Recurso experimental. Pode não funcionar para todos os back-ends.", "Slow": "<PERSON><PERSON>", "Fast": "<PERSON><PERSON><PERSON><PERSON>", "Play a sound when a message generation finishes": "Reproduzir um som quando a geração de mensagem termina", "Message Sound": "Som da mensagem", "Only play a sound when ST's browser tab is unfocused": "Reproduzir um som apenas quando a guia do navegador de ST não estiver focada", "Background Sound Only": "Apenas Som de Fundo", "Reduce the formatting requirements on API URLs": "Reduzir os requisitos de formatação nos URLs da API", "Relaxed API URLS": "URLs da API Relaxadas", "Ask to import the World Info/Lorebook for every new character with embedded lorebook. If unchecked, a brief message will be shown instead": "Pergunte se deseja importar as Informações Mundiais/Livro de Histórias para cada novo personagem com lorebook incorporado. Se não estiver marcado, será exibida uma breve mensagem em vez disso", "Lorebook Import Dialog": "Diálogo de Importação de Livro de Histórias", "Restore unsaved user input on page refresh": "Restaurar a entrada do usuário não salva ao atualizar a página", "Restore User Input": "Restaurar Entrada do Usuário", "Allow repositioning certain UI elements by dragging them. PC only, no effect on mobile": "Permitir reposicionamento de certos elementos da interface do usuário arrastando-os. Apenas PC, sem efeito no móvel", "Movable UI Panels": "Painéis de UI Móveis", "MovingUI preset. Predefined/saved draggable positions": "Predefinição MovingUI. Posições arrastáveis predefinidas/salvas", "MUI Preset": "Predefinição de MUI", "Save movingUI changes to a new file": "<PERSON><PERSON> as alterações de MovingUI em um novo arquivo", "Reset MovingUI panel sizes/locations.": "Redefinir tamanhos/locais do painel MovingUI.", "Apply a custom CSS style to all of the ST GUI": "Aplicar um estilo CSS personalizado a toda a GUI do ST", "Custom CSS": "CSS Personalizado", "Expand the editor": "Expanda o editor", "Chat/Message Handling": "Tratamento de Chat/Mensagem", "# Messages to Load": "# Msg. para carregar", "The number of chat history messages to load before pagination.": "O número de mensagens do histórico de bate-papo a serem carregadas antes da paginação.", "(0 = All)": "(0 = Todos)", "Streaming FPS": "FPS de Streaming", "Update speed of streamed text.": "Atualizar a velocidade do texto transmitido.", "Example Messages Behavior": "Comportamento de Mensagens de Exemplo", "Gradual push-out": "Empurrão gradual", "Always include examples": "Sempre incluir exemplos", "Never include examples": "Nunca incluir exemplos", "Send on Enter": "Enviar ao Pressionar Enter", "Disabled": "Desativado", "Automatic (PC)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (PC)", "Press Send to continue": "Pressione Enviar para continuar", "Show a button in the input area to ask the AI to continue (extend) its last message": "Mostrar um botão na área de entrada para pedir à IA que continue (estenda) sua última mensagem", "Quick 'Continue' button": "Bot<PERSON> 'Continuar' <PERSON>", "Show arrow buttons on the last in-chat message to generate alternative AI responses. Both PC and mobile": "Mostrar botões de seta na última mensagem no chat para gerar respostas alternativas da IA. Tanto no PC quanto no celular", "Swipes": "Swipes", "Allow using swiping gestures on the last in-chat message to trigger swipe generation. Mobile only, no effect on PC": "Permitir o uso de gestos de deslizamento na última mensagem no chat para acionar a geração de deslize. Apenas móvel, sem efeito no PC", "Gestures": "Gestos", "Auto-load Last Chat": "Carregar automaticamente o último Chat", "Auto-scroll Chat": "Chat de Rolagem Automática", "Save edits to messages without confirmation as you type": "Salvar edições em mensagens sem confirmação enquanto você digita", "Auto-save Message Edits": "Salvar automaticamente Edições de Mensagem", "Confirm message deletion": "Confirmar exclusão de mensagem", "Auto-fix Markdown": "Corrigir Markdown automaticamente", "Disallow embedded media from other domains in chat messages": "Proibir mídia incorporada de outros domínios em mensagens de bate-papo.", "Forbid External Media": "<PERSON><PERSON><PERSON>", "Allow {{char}}: in bot messages": "Permitir {{char}}: em mensagens de bot", "Allow {{user}}: in bot messages": "Permitir {{user}}: em mensagens de bot", "Skip encoding  and  characters in message text, allowing a subset of HTML markup as well as Markdown": "Pular a codificação de caracteres em texto de mensagem, permitindo um subconjunto de marcação HTML, bem como Markdown", "Show tags in responses": "Mostrar tags em respostas", "Allow AI messages in groups to contain lines spoken by other group members": "Permitir que mensagens de IA em grupos contenham linhas faladas por outros membros do grupo", "Relax message trim in Groups": "Reduzir corte de mensagem em Grupos", "Log prompts to console": "Registrar prompts no console", "Requests logprobs from the API for the Token Probabilities feature": "Solicita logprobs da API para o recurso de Probabilidades de Token", "Request token probabilities": "Solicitar probabilidades de token", "Automatically reject and re-generate AI message based on configurable criteria": "Rejeitar automaticamente e recriar mensagem de IA com base em critérios configuráveis", "Auto-swipe": "Auto-swipe", "Enable the auto-swipe function. Settings in this section only have an effect when auto-swipe is enabled": "Ativar a função de auto-swipe. As configurações nesta seção só têm efeito quando o auto-swipe está ativado", "Minimum generated message length": "Comprimento mínimo da mensagem gerada", "If the generated message is shorter than this, trigger an auto-swipe": "Se a mensagem gerada for mais curta que isso, acione um auto-swipe", "Blacklisted words": "<PERSON><PERSON><PERSON> proibidas", "words you dont want generated separated by comma ','": "palavras que você não quer geradas separadas por vírgula ','", "Blacklisted word count to swipe": "Contagem de palavras proibidas para swipe", "Minimum number of blacklisted words detected to trigger an auto-swipe": "Número mínimo de palavras proibidas detectadas para acionar um auto-swipe", "AutoComplete Settings": "Configurações de preenchimento automático", "Automatically hide details": "Ocultar detalhes automaticamente", "Determines how entries are found for autocomplete.": "Determina como as entradas são encontradas para preenchimento automático.", "Autocomplete Matching": "Coincidindo", "Starts with": "Começa com", "Includes": "Inclui", "Fuzzy": "<PERSON><PERSON><PERSON>", "Sets the style of the autocomplete.": "Define o estilo do preenchimento automático.", "Autocomplete Style": "<PERSON><PERSON><PERSON>", "Follow Theme": "<PERSON><PERSON><PERSON> te<PERSON>", "Dark": "Escuro", "Sets the font size of the autocomplete.": "Define o tamanho da fonte do preenchimento automático.", "Sets the width of the autocomplete.": "Define a largura do preenchimento automático.", "Autocomplete Width": "<PERSON><PERSON><PERSON>", "chat input box": "caixa de entrada de bate-papo", "entire chat width": "largura total do chat", "full window width": "largura total da janela", "STscript Settings": "Configurações STscript", "Sets default flags for the STscript parser.": "Define sinalizadores padrão para o analisador STscript.", "Parser Flags": "Sinalizadores de analisador", "Switch to stricter escaping, allowing all delimiting characters to be escaped with a backslash, and backslashes to be escaped as well.": "Mude para um escape mais estrito, permitindo que todos os caracteres delimitadores sejam escapados com uma barra invertida e que as barras invertidas também sejam escapadas.", "STRICT_ESCAPING": "STRICT_ESCAPING", "Replace all {{getvar::}} and {{getglobalvar::}} macros with scoped variables to avoid double macro substitution.": "Substitua todas as macros {{getvar::}} e {{getglobalvar::}} por variáveis ​​com escopo definido para evitar substituição dupla de macros.", "REPLACE_GETVAR": "REPLACE_GETVAR", "Change Background Image": "Alterar imagem de fundo", "Filter": "Filtro", "Automatically select a background based on the chat context": "Selecionar automaticamente um plano de fundo com base no contexto do chat", "Auto-select": "Seleção automática", "System Backgrounds": "Fundos do Sistema", "Chat Backgrounds": "Fundos de Chat", "bg_chat_hint_1": "Planos de fundo de bate-papo gerados com o", "bg_chat_hint_2": "a extensão aparecerá aqui.", "Extensions": "Extensões", "Notify on extension updates": "Notificar sobre atualizações de extensão", "Manage extensions": "Gerenciar extensões", "Import Extension From Git Repo": "Importar Extensão do Repositório Git", "Install extension": "Instalar extensão", "Extras API:": "API extras:", "Auto-connect": "Auto-conectar", "Extras API URL": "URL da API de extras", "Extras API key (optional)": "Chave da <PERSON> de extras (opcional)", "Persona Management": "Gerenciamento de Personagens", "How do I use this?": "Como eu uso isso?", "Click for stats!": "Clique para estatísticas!", "Usage Stats": "Estatísticas de uso", "Backup your personas to a file": "Faça uma cópia de segurança de suas personas para um arquivo", "Backup": "Cópia de Segurança", "Restore your personas from a file": "Restaure suas personas de um arquivo", "Restore": "Restaurar", "Create a dummy persona": "Criar uma persona fictícia", "Create": "<PERSON><PERSON><PERSON>", "Toggle grid view": "Alternar visualização de grade", "No persona description": "[Sem descrição]", "Name": "Nome", "Enter your name": "Digite seu nome", "Click to set a new User Name": "Clique para definir um novo nome de usuário", "Click to lock your selected persona to the current chat. Click again to remove the lock.": "Clique para travar sua persona selecionada no chat atual. Clique novamente para remover o travamento.", "Click to set user name for all messages": "Clique para definir o nome de usuário para todas as mensagens", "Persona Description": "Descrição da Persona", "Example: [{{user}} is a 28-year-old Romanian cat girl.]": "Exemplo: [{{user}} é uma garota gata romena de 28 anos.]", "Tokens persona description": "Descrição de tokens da persona", "Position:": "Posição:", "In Story String / Prompt Manager": "Na sequência de história / Gerenciador de prompt", "Top of Author's Note": "Topo da nota do autor", "Bottom of Author's Note": "Fundo da nota do autor", "In-chat @ Depth": "No chat @ Depth", "Depth:": "Profundidade:", "Role:": "Papel:", "System": "Sistema", "User": "Do utilizador", "Assistant": "<PERSON><PERSON><PERSON>", "Show notifications on switching personas": "Mostrar notificações ao alternar personas", "Character Management": "Gerenciamento de personagens", "Locked = Character Management panel will stay open": "Trancado = O painel de gerenciamento de personagens permanecerá aberto", "Select/Create Characters": "Se<PERSON>cionar/Criar <PERSON>", "Favorite characters to add them to HotSwaps": "Favoritar personagens para adicioná-los aos HotSwaps", "Token counts may be inaccurate and provided just for reference.": "As contagens de tokens podem ser imprecisas e fornecidas apenas para referência.", "Total tokens": "<PERSON><PERSON><PERSON> de fi<PERSON>s", "Calculating...": "Calculando...", "Tokens": "Tokens", "Permanent tokens": "Tokens permanentes", "Permanent": "Permanente", "About Token 'Limits'": "Sobre os 'Limites' do Token", "Toggle character info panel": "Alternar painel de informações do personagem", "Name this character": "Nomeie este personagem", "extension_token_counter": "Fichas:", "Click to select a new avatar for this character": "Clique para selecionar um novo avatar para este personagem", "Add to Favorites": "Adicionar aos favoritos", "Advanced Definition": "Definição avançada", "Character Lore": "Lore do personagem", "Chat Lore": "História do bate-papo", "Export and Download": "Exportar e baixar", "Duplicate Character": "Duplicar personagem", "Create Character": "Criar personagem", "Delete Character": "Excluir personagem", "More...": "Mais...", "Link to World Info": "Link para informações do mundo", "Import Card Lore": "Importar lore do cartão", "Scenario Override": "Substituição de cenário", "Convert to Persona": "Converter para Persona", "Rename": "Renomear", "Link to Source": "Link para a fonte", "Replace / Update": "Substituir/Atualizar", "Import Tags": "Importar tags", "Search / Create Tags": "Pesquisar / Criar tags", "View all tags": "Ver todas as tags", "Creator's Notes": "Notas do criador", "Show / Hide Description and First Message": "Mostrar / Ocultar Descrição e Primeira Mensagem", "Character Description": "Descrição do personagem", "Click to allow/forbid the use of external media for this character.": "Clique para permitir/proibir o uso de mídia externa para este personagem.", "Ext. Media": "Mídia <PERSON>", "Describe your character's physical and mental traits here.": "Descreva os traços físicos e mentais do seu personagem aqui.", "First message": "Primeira mensagem", "Click to set additional greeting messages": "Clique para definir mensagens de saudação adicionais", "Alt. Greetings": "Alt. Saudações", "This will be the first message from the character that starts every chat.": "Esta será a primeira mensagem do personagem que inicia cada chat.", "Group Controls": "Controles de Grupo", "Chat Name (Optional)": "Nome do chat (opcional)", "Click to select a new avatar for this group": "Clique para selecionar um novo avatar para este grupo", "Group reply strategy": "Estratégia de resposta em grupo", "Natural order": "Ordem natural", "List order": "Ordem da lista", "Group generation handling mode": "Modo de tratamento de geração de grupo", "Swap character cards": "Trocar cartas de personagem", "Join character cards (exclude muted)": "Junte-se a cartões de personagem (exclua silenciado)", "Join character cards (include muted)": "Junte-se a cartões de personagem (inclua silenciado)", "Inserted before each part of the joined fields.": "Inserido antes de cada parte dos campos unidos.", "Join Prefix": "Prefixo de adesão", "When 'Join character cards' is selected, all respective fields of the characters are being joined together.This means that in the story string for example all character descriptions will be joined to one big text.If you want those fields to be separated, you can define a prefix or suffix here.This value supports normal macros and will also replace {{char}} with the relevant char's name and <FIELDNAME> with the name of the part (e.g.: description, personality, scenario, etc.)": "Quando 'Unir cartas de personagem' é selecionado, todos os respectivos campos dos personagens são unidos.\rIsto significa que na sequência da história, por exemplo, todas as descrições dos personagens serão unidas a um grande texto.\rSe quiser que esses campos sejam separados, você pode definir um prefixo ou sufixo aqui.\r\rEste valor suporta macros normais e também substituirá {{char}} pelo nome do char relevante e <FIELDNAME> pelo nome da parte (por exemplo: descrição, personalidade, cenário, etc.)", "Inserted after each part of the joined fields.": "Inserido após cada parte dos campos unidos.", "Join Suffix": "Junte-se ao sufixo", "Set a group chat scenario": "Definir um cenário de bate-papo em grupo", "Click to allow/forbid the use of external media for this group.": "Clique para permitir/proibir o uso de mídia externa para este grupo.", "Restore collage avatar": "Restaurar avatar de colagem", "Allow self responses": "<PERSON><PERSON><PERSON> respost<PERSON> pr<PERSON>", "Auto Mode": "<PERSON><PERSON>", "Auto Mode delay": "Atraso no modo automático", "Hide Muted Member Sprites": "Ocultar Sprites de Membros Silenciados", "Current Members": "<PERSON><PERSON><PERSON>", "Add Members": "<PERSON><PERSON><PERSON><PERSON>", "Create New Character": "Criar novo personagem", "Import Character from File": "Importar personagem do arquivo", "Import content from external URL": "Importar conteúdo de URL externa", "Create New Chat Group": "Criar novo grupo de bate-papo", "Characters sorting order": "Ordem de classificação dos personagens", "A-Z": "A-Z", "Z-A": "Z-A", "Newest": "<PERSON><PERSON>e", "Oldest": "<PERSON><PERSON> anti<PERSON>", "Favorites": "<PERSON><PERSON><PERSON><PERSON>", "Recent": "<PERSON><PERSON>", "Most chats": "<PERSON><PERSON> dos chats", "Least chats": "<PERSON><PERSON> chats", "Most tokens": "Mais tokens", "Least tokens": "Menos tokens", "Random": "<PERSON><PERSON><PERSON><PERSON>", "Toggle character grid view": "Alternar visualização em grade de personagem", "Bulk_edit_characters": "Editar personagens em massa", "Bulk select all characters": "Selecione em massa todos os caracteres", "Bulk delete characters": "Excluir personagens em massa", "popup-button-save": "<PERSON><PERSON>", "popup-button-yes": "<PERSON>m", "popup-button-no": "Não", "popup-button-cancel": "<PERSON><PERSON><PERSON>", "popup-button-import": "Importar", "Advanced Definitions": "Definições Avançadas", "Prompt Overrides": "Substituições de prompt", "(For Chat Completion and Instruct Mode)": "(Para conclusão de bate-papo e modo de instrução)", "Insert {{original}} into either box to include the respective default prompt from system settings.": "Insira {{original}} em qualquer caixa para incluir o prompt padrão respectivo das configurações do sistema.", "Main Prompt": "Prompt principal", "Any contents here will replace the default Main Prompt used for this character. (v2 spec: system_prompt)": "Qualquer conteúdo aqui substituirá o prompt principal padr<PERSON> usado para este personagem. (especificação v2: system_prompt)", "Any contents here will replace the default Jailbreak Prompt used for this character. (v2 spec: post_history_instructions)": "Qualquer conteúdo aqui substituirá o prompt de jailbreak padr<PERSON> usado para este personagem. (especificação v2: post_history_instructions)", "Creator's Metadata (Not sent with the AI prompt)": "Metadados do criador (Não enviado com o prompt da IA)", "Creator's Metadata": "Metadados do Criador", "(Not sent with the AI Prompt)": "(Não enviado com o prompt AI)", "Everything here is optional": "Tudo aqui é opcional", "(Botmaker's name / Contact Info)": "(Nome do fabricante do bot / Informações de contato)", "(If you want to track character versions)": "(Se você deseja rastrear versões do personagem)", "(Describe the bot, give use tips, or list the chat models it has been tested on. This will be displayed in the character list.)": "(<PERSON><PERSON><PERSON> o bot, dê dicas de uso ou liste os modelos de chat nos quais ele foi testado. <PERSON><PERSON> será exibido na lista de personagens.)", "Tags to Embed": "Tags para incorporar", "(Write a comma-separated list of tags)": "(Escreva uma lista de tags separadas por vírgulas)", "Personality summary": "<PERSON><PERSON><PERSON> da Personalidade", "(A brief description of the personality)": "(Uma breve descrição da personalidade)", "Scenario": "<PERSON><PERSON><PERSON>", "(Circumstances and context of the interaction)": "(Circunstâncias e contexto da interação)", "Character's Note": "Nota do personagem", "(Text to be inserted in-chat @ designated depth and role)": "(Texto a ser inserido no chat na profundidade e função designadas)", "@ Depth": "@ Profundidade", "Role": "Papel", "Talkativeness": "Loquacidade", "How often the character speaks in group chats!": "Com que frequência o personagem fala em chats em grupo!", "How often the character speaks in": "Com que frequência o personagem fala", "group chats!": "chats em grupo!", "Shy": "<PERSON><PERSON><PERSON><PERSON>", "Normal": "Normal", "Chatty": "Conversador", "Examples of dialogue": "Exemplos de diálogo", "Important to set the character's writing style.": "Importante definir o estilo de escrita do personagem.", "(Examples of chat dialog. Begin each example with START on a new line.)": "(Exemplos de diálogo de chat. Comece cada exemplo com START em uma nova linha.)", "Save": "<PERSON><PERSON>", "Chat History": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Import Chat": "Importar bate-papo", "Copy to system backgrounds": "Copiar para planos de fundo do sistema", "Rename background": "Renomear plano de fundo", "Lock": "Trancar", "Unlock": "Desb<PERSON>que<PERSON>", "Delete background": "Excluir plano de fundo", "Chat Scenario Override": "Substituição de cenário de bate-papo", "Remove": "Remover", "Type here...": "Digite aqui...", "Chat Lorebook": "Lorebook de bate-papo para", "Chat Lorebook for": "Lorebook de bate-papo para", "chat_world_template_txt": "Uma informação mundial selecionada será vinculada a este chat. Ao gerar uma resposta de IA,\n                    ele será combinado com as entradas dos livros de história global e de personagens.", "Select a World Info file for": "Selecionar um arquivo de informações do mundo para", "Primary Lorebook": "Livro de lore primário", "A selected World Info will be bound to this character as its own Lorebook.": "Um World Info selecionado será vinculado a este personagem como seu próprio Livro de Lore.", "When generating an AI reply, it will be combined with the entries from a global World Info selector.": "Ao gerar uma resposta da IA, ela será combinada com as entradas de um seletor global de Informações do Mundo.", "Exporting a character would also export the selected Lorebook file embedded in the JSON data.": "Exportar um personagem também exportaria o arquivo de Livro de Lore selecionado incorporado nos dados JSON.", "Additional Lorebooks": "Livros de lore adicionais", "Associate one or more auxillary Lorebooks with this character.": "Associar um ou mais Livros de Lore auxiliares a este personagem.", "NOTE: These choices are optional and won't be preserved on character export!": "NOTA: Essas escolhas são opcionais e não serão preservadas na exportação do personagem!", "Rename chat file": "Renomear arquivo de bate-papo", "Export JSONL chat file": "Exportar arquivo de bate-papo JSONL", "Download chat as plain text document": "Baixar bate-papo como documento de texto simples", "Delete chat file": "Excluir arquivo de bate-papo", "Use tag as folder": "Marcar como pasta", "Delete tag": "Excluir tag", "Entry Title/Memo": "<PERSON><PERSON><PERSON><PERSON> da Entrada/Memo", "WI Entry Status:🔵 Constant🟢 Normal🔗 Vectorized❌ Disabled": "Status de entrada WI:\r🔵 Constante\r🟢 Normais\r🔗 Vetorizado\r❌ Desativado", "WI_Entry_Status_Constant": "<PERSON><PERSON><PERSON>", "WI_Entry_Status_Normal": "Normal", "WI_Entry_Status_Vectorized": "Vetorizado", "WI_Entry_Status_Disabled": "Desabilitado", "T_Position": "↑Char: antes das definições de caractere\n↓Char: após as definições de caractere\n↑AN: antes das notas do autor\n↓AN: após as notas do autor\n@D: em profundidade", "Before Char Defs": "Antes das definições de caractere", "After Char Defs": "<PERSON><PERSON>ós as definições de caractere", "Before EM": "↑ EM", "After EM": "↓EM", "Before AN": "Antes do <PERSON>", "After AN": "Após o AN", "at Depth System": "@D⚙️", "at Depth User": "@D 👤", "at Depth AI": "@D 🤖", "Depth": "Profundidade", "Order:": "Ordem:", "Order": "Ordem:", "Trigger %:": "Acionar %:", "Probability": "Probabilidade", "Duplicate world info entry": "Entrada de informações mundiais duplicada", "Delete world info entry": "Excluir entrada de informações mundiais", "Comma separated (required)": "Separado por vírgula (obrigatório)", "Primary Keywords": "Palavras-chave primárias", "Keywords or Regexes": "Palavras-chave ou Regexes", "Comma separated list": "Lista separada por vírgulas", "Switch to plaintext mode": "Mudar para o modo de texto simples", "Logic": "Lógica", "AND ANY": "E QUALQUER", "AND ALL": "E TODOS", "NOT ALL": "NENHUM DE TODOS", "NOT ANY": "NENHUM DE QUALQUER", "(ignored if empty)": "(ignorado se estiver vazio)", "Optional Filter": "Filtro opcional", "Keywords or Regexes (ignored if empty)": "Palavras-chave ou Regexes (ignorados se vazios)", "Comma separated list (ignored if empty)": "Lista separada por vírgulas (ignorada se estiver vazia)", "Use global setting": "Usar configuração global", "Case-Sensitive": "Diferencia maiúsculas de minúsculas", "Yes": "<PERSON>m", "No": "Não", "Can be used to automatically activate Quick Replies": "Pode ser usado para ativar automaticamente as Respostas <PERSON>", "Automation ID": "ID de automação", "( None )": "( <PERSON>enhum )", "Content": "<PERSON><PERSON><PERSON><PERSON>", "Exclude from recursion": "Excluir da recursão", "Prevent further recursion (this entry will not activate others)": "Impedir mais recursões (esta entrada não ativará outras)", "Delay until recursion (this entry can only be activated on recursive checking)": "Atraso até a recursão (esta entrada só pode ser ativada na verificação recursiva)", "What this keyword should mean to the AI, sent verbatim": "O que essa palavra-chave deve significar para a IA, enviada literalmente", "Filter to Character(s)": "Filtrar para Personagem(s)", "Character Exclusion": "Exclusão de personagem", "-- Characters not found --": "-- Personagens não encontrados --", "Inclusion Group": "Grupo de inclusão", "Inclusion Groups ensure only one entry from a group is activated at a time, if multiple are triggered.Documentation: World Info - Inclusion Group": "Os Grupos de Inclusão garantem que apenas uma entrada de um grupo seja ativada por vez, se várias forem acionadas.\rSuporta vários grupos separados por vírgula.\r\rDocumentação: World Info - Grupo de Inclusão", "Prioritize this entry: When checked, this entry is prioritized out of all selections.If multiple are prioritized, the one with the highest 'Order' is chosen.": "Priorizar esta entrada: quando marcada, esta entrada é priorizada entre todas as seleções.\rSe vários forem priorizados, aquele com a “Ordem” mais alta será escolhido.", "Only one entry with the same label will be activated": "Apenas uma entrada com o mesmo rótulo será ativada", "A relative likelihood of entry activation within the group": "Uma probabilidade relativa de ativação de entrada dentro do grupo", "Group Weight": "Peso do grupo", "Selective": "Seletivo", "Use Probability": "Usar Probabilidade", "Add Memo": "<PERSON><PERSON><PERSON><PERSON> memorando", "Text or token ids": "Texto ou [IDs de token]", "close": "fechar", "prompt_manager_edit": "<PERSON><PERSON>", "prompt_manager_name": "Nome", "A name for this prompt.": "Um nome para este prompt.", "To whom this message will be attributed.": "A quem esta mensagem será atribuída.", "AI Assistant": "Assistente de IA", "prompt_manager_position": "Posição", "Injection position. Next to other prompts (relative) or in-chat (absolute).": "Posição de injeção. Ao lado de outras solicitações (relativas) ou no chat (absolutas).", "prompt_manager_relative": "Relativo", "prompt_manager_depth": "Profundidade", "Injection depth. 0 = after the last message, 1 = before the last message, etc.": "Profundidade de injeção. 0 = após a última mensagem, 1 = antes da última mensagem, etc.", "Prompt": "Prompt", "The prompt to be sent.": "O prompt a ser enviado.", "This prompt cannot be overridden by character cards, even if overrides are preferred.": "Este prompt não pode ser substituído por cartas de personagem, mesmo que as substituições sejam preferidas.", "prompt_manager_forbid_overrides": "<PERSON>ibir substitui<PERSON>", "reset": "reiniciar", "save": "salvar", "This message is invisible for the AI": "Esta mensagem é invisível para a IA", "Message Actions": "Ações de mensagens", "Translate message": "Traduzir mensagem", "Generate Image": "<PERSON><PERSON><PERSON> imagem", "Narrate": "<PERSON><PERSON><PERSON>", "Exclude message from prompts": "Excluir mensagem de prompts", "Include message in prompts": "Incluir mensagem em prompts", "Embed file or image": "Incorporar arquivo ou imagem", "Create checkpoint": "Criar ponto de verificação", "Create Branch": "<PERSON><PERSON><PERSON> ramo", "Copy": "Copiar", "Open checkpoint chat": "<PERSON><PERSON><PERSON> bate-papo do ponto de verificação", "Edit": "<PERSON><PERSON>", "Confirm": "Confirmar", "Copy this message": "Copiar esta mensagem", "Delete this message": "Excluir esta mensagem", "Move message up": "Mover mensagem para cima", "Move message down": "Mover mensagem para baixo", "Enlarge": "Aumentar", "Welcome to SillyTavern!": "Bem-vindo ao SillyTavern!", "welcome_message_part_1": "<PERSON><PERSON> o", "welcome_message_part_2": "Documentação Oficial", "welcome_message_part_3": null, "welcome_message_part_4": "Tipo", "welcome_message_part_5": "no chat para comandos e macros.", "welcome_message_part_6": "Junte-se a", "Discord server": "<PERSON><PERSON><PERSON>", "welcome_message_part_7": "para informações e anúncios.", "SillyTavern is aimed at advanced users.": "SillyTavern é voltado para usuários avançados.", "If you're new to this, enable the simplified UI mode below.": "Se você é novo nisso, habilite o modo de UI simplificado abaixo.", "Change it later in the 'User Settings' panel.": "Altere-o posteriormente no painel ‘Configurações do usuário’.", "Enable simple UI mode": "Ative o modo UI simples", "Looking for AI characters?": "Procurando por personagens de IA?", "onboarding_import": "Importar", "from supported sources or view": "de fontes suportadas ou visualização", "Sample characters": "Personagens de amostra", "Your Persona": "<PERSON><PERSON>", "Before you get started, you must select a persona name.": "<PERSON><PERSON> de começar, você deve selecionar um nome de persona.", "welcome_message_part_8": "<PERSON><PERSON> pode ser alterado a qualquer momento através do", "welcome_message_part_9": "ícone.", "Persona Name:": "<PERSON><PERSON> da Pessoa:", "Temporarily disable automatic replies from this character": "Desativar temporariamente respostas automáticas deste personagem", "Enable automatic replies from this character": "Ativar respostas automáticas deste personagem", "Trigger a message from this character": "Disparar uma mensagem deste personagem", "Move up": "Mover para cima", "Move down": "Mover para baixo", "View character card": "Ver cartão de personagem", "Remove from group": "Remover do grupo", "Add to group": "Adicionar ao grupo", "Alternate Greetings": "Saudações alternativas", "Alternate_Greetings_desc": "Eles serão exibidos como swipes na primeira mensagem ao iniciar um novo chat.\nOs membros do grupo podem selecionar um deles para iniciar a conversa.", "Alternate Greetings Hint": "Clique no botão para começar!", "(This will be the first message from the character that starts every chat)": "(Esta será a primeira mensagem do personagem que inicia cada chat)", "Forbid Media Override explanation": "Capacidade do personagem/grupo atual de usar mídia externa em chats.", "Forbid Media Override subtitle": "Mídia: <PERSON><PERSON>, v<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Externo: não hospedado no servidor local.", "Always forbidden": "Sempre proibido", "Always allowed": "Sempre permitido", "View contents": "<PERSON><PERSON> con<PERSON><PERSON>", "Remove the file": "Remova o arquivo", "Unique to this chat": "Exclusivo para este chat", "Checkpoints inherit the Note from their parent, and can be changed individually after that.": "Os pontos de verificação herdam a nota de seu pai e podem ser alterados individualmente depois disso.", "Include in World Info Scanning": "Incluir na varredura de informações mundiais", "Before Main Prompt / Story String": "Antes do prompt principal/string da história", "After Main Prompt / Story String": "Após o prompt principal/sequência de história", "as": "como", "Insertion Frequency": "Frequência de Inserção", "(0 = Disable, 1 = Always)": "(0 = Desativar, 1 = Sempre)", "User inputs until next insertion:": "Entradas do usuário até a próxima inserção:", "Character Author's Note (Private)": "Nota do autor do personagem (privado)", "Won't be shared with the character card on export.": "Não será compartilhado com o cartão de personagem na exportação.", "Will be automatically added as the author's note for this character. Will be used in groups, but can't be modified when a group chat is open.": "Será adicionado automaticamente como nota do autor para este personagem. Será usado em grupos, mas\n                            não pode ser modificado quando um bate-papo em grupo está aberto.", "Use character author's note": "Use a nota do autor do personagem", "Replace Author's Note": "Substituir nota do autor", "Default Author's Note": "Nota do autor padrão", "Will be automatically added as the Author's Note for all new chats.": "Será adicionado automaticamente como Nota do Autor para todos os novos chats.", "Chat CFG": "Bate-papo CFG", "1 = disabled": "1 = desativado", "write short replies, write replies using past tense": "escreva respostas curtas, escreva respostas usando o pretérito", "Positive Prompt": "Alerta Positivo", "Use character CFG scales": "Use escalas CFG de caracteres", "Character CFG": "Personagem CFG", "Will be automatically added as the CFG for this character.": "Será adicionado automaticamente como CFG para este personagem.", "Global CFG": "CFG global", "Will be used as the default CFG options for every chat unless overridden.": "Serão usadas como opções CFG padrão para todos os chats, a menos que sejam substituídas.", "CFG Prompt Cascading": "Cascata de prompt CFG", "Combine positive/negative prompts from other boxes.": "Combine prompts positivos/negativos de outras caixas.", "For example, ticking the chat, global, and character boxes combine all negative prompts into a comma-separated string.": "Por exemplo, marcar as caixas de bate-papo, global e de caracteres combina todos os prompts negativos em uma string separada por vírgula.", "Always Include": "Sempre incluir", "Chat Negatives": "Negativos de bate-papo", "Character Negatives": "Negativos de caracteres", "Global Negatives": "Negativos Globais", "Custom Separator:": "Separador personalizado:", "Insertion Depth:": "Profundidade de inserção:", "Token Probabilities": "Probabilidades de token", "Select a token to see alternatives considered by the AI.": "Selecione um token para ver as alternativas consideradas pela IA.", "Not connected to API!": "Não conectado à API!", "Type a message, or /? for help": "Digite uma mensagem ou /? para ajuda", "Continue script execution": "Continuar a execução do script", "Pause script execution": "Pausar a execução do script", "Abort script execution": "Abortar a execução do script", "Abort request": "Cancelar solicitação", "Continue the last message": "Continuar a última mensagem", "Send a message": "Enviar uma mensagem", "Close chat": "<PERSON><PERSON><PERSON> chat", "Toggle Panels": "<PERSON><PERSON><PERSON>", "Back to parent chat": "Voltar para o chat anterior", "Save checkpoint": "Salvar ponto de verificação", "Convert to group": "Converter em grupo", "Start new chat": "Iniciar novo chat", "Manage chat files": "Gerenciar arquivos de chat", "Delete messages": "Excluir mensagens", "Regenerate": "<PERSON><PERSON><PERSON>", "Ask AI to write your message for you": "Peça à IA para escrever sua mensagem para você", "Impersonate": "Personificar", "Continue": "<PERSON><PERSON><PERSON><PERSON>", "Bind user name to that avatar": "Associar nome de usuário a esse avatar", "Change persona image": "<PERSON><PERSON><PERSON> imagem da persona", "Select this as default persona for the new chats.": "Selecionar isso como persona padrão para os novos chats.", "Delete persona": "Excluir persona", "These characters are the winners of character design contests and have outstandable quality.": "Esses personagens são vencedores de concursos de design de personagens e possuem qualidade notável.", "Contest Winners": "Vencedores do concurso", "These characters are the finalists of character design contests and have remarkable quality.": "Esses personagens são finalistas de concursos de design de personagens e possuem qualidade notável.", "Featured Characters": "Personagens em destaque", "Attach a File": "Anexar um arquivo", "Open Data Bank": "Banco de dados aberto", "Enter a URL or the ID of a Fandom wiki page to scrape:": "Insira um URL ou o ID de uma página wiki do Fandom para extrair:", "Examples:": "Exemplos:", "Example:": "Exemplo:", "Single file": "Único arquivo", "All articles will be concatenated into a single file.": "Todos os artigos serão concatenados em um único arquivo.", "File per article": "Arquivo por artigo", "Each article will be saved as a separate file.": "Não recomendado. Cada artigo será salvo como um arquivo separado.", "Data Bank": "Banco de dados", "These files will be available for extensions that support attachments (e.g. Vector Storage).": "Esses arquivos estarão disponíveis para extensões que suportam anexos (por exemplo, Vector Storage).", "Supported file types: Plain Text, PDF, Markdown, HTML, EPUB.": "Tipos de arquivos suportados: Texto Simples, PDF, Markdown, HTML, EPUB.", "Drag and drop files here to upload.": "Arraste e solte os arquivos aqui para fazer upload.", "Date (Newest First)": "Data (mais recente primeiro)", "Date (Oldest First)": "Data (mais antigo primeiro)", "Name (A-Z)": "Nome (A-Z)", "Name (Z-A)": "Nome (Z-A)", "Size (Smallest First)": "<PERSON><PERSON><PERSON> (menor primeiro)", "Size (Largest First)": "<PERSON><PERSON><PERSON> (maior primeiro)", "Bulk Edit": "Edição em massa", "Select All": "Selecionar tudo", "Select None": "<PERSON><PERSON><PERSON><PERSON> nenh<PERSON>", "Global Attachments": "Anexos Globais", "These files are available for all characters in all chats.": "Esses arquivos estão disponíveis para todos os personagens em todos os chats.", "Character Attachments": "Anexos de personagens", "These files are available the current character in all chats they are in.": "Esses arquivos estão disponíveis no personagem atual em todos os chats em que eles participam.", "Saved locally. Not exported.": "Salvo localmente. Não exportado.", "Chat Attachments": "Anexos de bate-papo", "These files are available to all characters in the current chat.": "Esses arquivos estão disponíveis para todos os personagens do chat atual.", "Enter a base URL of the MediaWiki to scrape.": "Insira um URL base do MediaWiki para extrair.", "Don't include the page name!": "Não inclua o nome da página!", "Enter web URLs to scrape (one per line):": "Insira URLs da web para extrair (um por linha):", "Enter a video URL to download its transcript.": "Insira o URL ou ID do vídeo para baixar sua transcrição.", "Expression API": "Local\nExtras\nLLM", "ext_sum_with": "Resuma com:", "ext_sum_main_api": "API principal", "ext_sum_current_summary": "Resumo atual:", "ext_sum_restore_previous": "Restaurar anterior", "ext_sum_memory_placeholder": "O resumo será gerado aqui...", "Trigger a summary update right now.": "<PERSON><PERSON><PERSON> agora", "ext_sum_force_text": "<PERSON><PERSON><PERSON> agora", "Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API).": "Desative as atualizações automáticas de resumo. Durante a pausa, o resumo permanece como está. Você ainda pode forçar uma atualização pressionando o botão Resumir agora (que está disponível apenas na API principal).", "ext_sum_pause": "Pausa", "Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.": "Omitir Informações Mundiais e Nota do Autor do texto a ser resumido. Só tem efeito ao usar a API principal. A API Extras sempre omite WI/AN.", "ext_sum_no_wi_an": "Sem WI/AN", "ext_sum_settings_tip": "Edite o prompt de resumo, posição de inserção, etc.", "ext_sum_settings": "Configurações de resumo", "ext_sum_prompt_builder": "Construtor de prompt", "ext_sum_prompt_builder_1_desc": "A extensão criará seu próprio prompt usando mensagens que ainda não foram resumidas. Bloqueia o chat até que o resumo seja gerado.", "ext_sum_prompt_builder_1": "Cru, bloqueando", "ext_sum_prompt_builder_2_desc": "A extensão construirá seu próprio prompt usando mensagens que ainda não foram resumidas. Não bloqueia o chat enquanto o resumo está sendo gerado. Nem todos os backends suportam este modo.", "ext_sum_prompt_builder_2": "<PERSON><PERSON><PERSON>, sem bloqueio", "ext_sum_prompt_builder_3_desc": "A extensão usará o construtor de prompt principal regular e adicionará a solicitação de resumo a ele como a última mensagem do sistema.", "ext_sum_prompt_builder_3": "Clássico, bloqueio", "Summary Prompt": "Solicitação de resumo", "ext_sum_restore_default_prompt_tip": "Restaurar prompt padr<PERSON>", "ext_sum_prompt_placeholder": "Este prompt será enviado ao AI para solicitar a geração do resumo. {{words}} será resolvido para o parâmetro 'Número de palavras'.", "ext_sum_target_length_1": "Comprimento do resumo do alvo", "ext_sum_target_length_2": null, "ext_sum_target_length_3": "palavras)", "ext_sum_api_response_length_1": "Comprimento da resposta da API", "ext_sum_api_response_length_2": null, "ext_sum_api_response_length_3": "fichas)", "ext_sum_0_default": "0 = padr<PERSON>", "ext_sum_raw_max_msg": "[Raw] Máximo de mensagens por solicitação", "ext_sum_0_unlimited": "0 = ilimitado", "Update frequency": "Frequência de atualização", "ext_sum_update_every_messages_1": "Atualizar a cada", "ext_sum_update_every_messages_2": "mensagens", "ext_sum_0_disable": "0 = desabilitar", "ext_sum_auto_adjust_desc": "Tente ajustar automaticamente o intervalo com base nas métricas do chat.", "ext_sum_update_every_words_1": "Atualizar a cada", "ext_sum_update_every_words_2": "palavras", "ext_sum_both_sliders": "Se ambos os controles deslizantes forem diferentes de zero, ambos acionarão atualizações de resumo em seus respectivos intervalos.", "ext_sum_injection_template": "Modelo de injeção", "ext_sum_memory_template_placeholder": "{{summary}} resolver<PERSON> o conteúdo do resumo atual.", "ext_sum_injection_position": "Posição de injeção", "How many messages before the current end of the chat.": "Quantas mensagens antes do final atual do chat.", "ext_regex_title": "Regex", "ext_regex_new_global_script": "+Global", "ext_regex_new_scoped_script": "+ Escopo", "ext_regex_import_script": "Importar", "ext_regex_global_scripts": "Scripts Globais", "ext_regex_global_scripts_desc": "Disponível para todos os personagens. Salvo nas configurações locais.", "ext_regex_scoped_scripts": "Scripts com escopo", "ext_regex_scoped_scripts_desc": "Disponível apenas para este personagem. Salvo nos dados do cartão.", "Regex Editor": "Editor <PERSON><PERSON>", "Test Mode": "<PERSON><PERSON> de teste", "ext_regex_desc": "Regex é uma ferramenta para encontrar/substituir strings usando expressões regulares. Se quiser saber mais, clique no ? ao lado do título.", "Input": "Entrada", "ext_regex_test_input_placeholder": "Digite aqui...", "Output": "<PERSON><PERSON><PERSON>", "ext_regex_output_placeholder": "<PERSON><PERSON><PERSON>", "Script Name": "Nome do roteiro", "Find Regex": "Encontre Regex", "Replace With": "Substituir com", "ext_regex_replace_string_placeholder": "Use {{match}} para incluir o texto correspondente do Find Regex ou $1, $2, etc. para grupos de captura.", "Trim Out": "<PERSON><PERSON><PERSON>", "ext_regex_trim_placeholder": "Corta globalmente quaisquer peças indesejadas de uma correspondência de regex antes da substituição. Separe cada elemento com um enter.", "ext_regex_affects": "Afeta", "ext_regex_user_input": "Entrada do usuário", "ext_regex_ai_output": "Saída de IA", "Slash Commands": "Comandos de bar<PERSON>", "ext_regex_min_depth_desc": "Quando aplicado a prompts ou exibição, afeta apenas mensagens com pelo menos N níveis de profundidade. 0 = última mensagem, 1 = penúltima mensagem, etc. Conta apenas entradas WI @Depth e mensagens utilizáveis, ou seja, não ocultas ou do sistema.", "Min Depth": "Profundidade mínima", "ext_regex_min_depth_placeholder": "<PERSON><PERSON><PERSON><PERSON>", "ext_regex_max_depth_desc": "Quando aplicado a prompts ou exibição, afeta apenas mensagens com profundidade não superior a N. 0 = última mensagem, 1 = penúltima mensagem, etc. Conta apenas entradas WI @Depth e mensagens utilizáveis, ou seja, não ocultas ou do sistema.", "ext_regex_other_options": "Outras opções", "Only Format Display": "Somente formato de exibição", "ext_regex_only_format_prompt_desc": "O histórico de bate-papo não será alterado, apenas o prompt conforme a solicitação é enviada (na geração).", "Only Format Prompt (?)": "Apenas prompt de formato", "Run On Edit": "Executar na edição", "ext_regex_substitute_regex_desc": "Substitua {{macros}} em Find Regex antes de executá-lo", "Substitute Regex": "Substituir Regex", "ext_regex_import_target": "Importar para:", "ext_regex_disable_script": "Desativar script", "ext_regex_enable_script": "Habilitar script", "ext_regex_edit_script": "<PERSON><PERSON>", "ext_regex_move_to_global": "Mude para scripts globais", "ext_regex_move_to_scoped": "Mover para scripts com escopo definido", "ext_regex_export_script": "Exportar script", "ext_regex_delete_script": "<PERSON>cluir rot<PERSON>", "Trigger Stable Diffusion": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "sd_Yourself": "<PERSON><PERSON><PERSON> mesmo", "sd_Your_Face": "Seu rosto", "sd_Me": "<PERSON><PERSON>", "sd_The_Whole_Story": "A história toda", "sd_The_Last_Message": "A última mensagem", "sd_Raw_Last_Message": "Última mensagem bruta", "sd_Background": "Fundo", "Image Generation": "Geração de imagem", "sd_refine_mode": "Permitir editar prompts manualmente antes de enviá-los para a API de geração", "sd_refine_mode_txt": "Edite os prompts antes da geração", "sd_interactive_mode": "Gere imagens automaticamente ao enviar mensagens como 'mande-me uma foto de gato'.", "sd_interactive_mode_txt": "Modo interativo", "sd_multimodal_captioning": "Use legendas multimodais para gerar prompts para retratos de usuários e personagens com base em seus avatares.", "sd_multimodal_captioning_txt": "Use legendas multimodais para retratos", "sd_expand": "Estenda prompts automaticamente usando modelo de geração de texto", "sd_expand_txt": "Solicitações de aprimoramento automático", "sd_snap": "Solicitações de geração de snap com uma proporção de aspecto forçada (retratos, planos de fundo) para a resolução conhecida mais próxima, enquanto tenta preservar as contagens absolutas de pixels (recomendado para SDXL).", "sd_snap_txt": "Tire resoluções ajustadas automaticamente", "Source": "Fonte", "sd_auto_url": "Exemplo: {{auto_url}}", "Authentication (optional)": "Autenticação (opcional)", "Example: username:password": "Exemplo: nome de usuário:senha", "Important:": "Importante:", "sd_auto_auth_warning_1": "execute SD Web UI com o", "sd_auto_auth_warning_2": "bandeira! O servidor deve estar acessível a partir da máquina host <PERSON><PERSON>T<PERSON><PERSON>.", "sd_drawthings_url": "Exemplo: {{drawthings_url}}", "sd_drawthings_auth_txt": "execute o aplicativo DrawThings com a opção HTTP API habilitada na IU! O servidor deve estar acessível a partir da máquina host <PERSON>llyTavern.", "sd_vlad_url": "Exemplo: {{vlad_url}}", "The server must be accessible from the SillyTavern host machine.": "O servidor deve estar acessível a partir da máquina host <PERSON><PERSON><PERSON><PERSON><PERSON>.", "Hint: Save an API key in AI Horde API settings to use it here.": "Dica: salve uma chave de API nas configurações da API AI Horde para usá-la aqui.", "Allow NSFW images from Horde": "Permitir imagens NSFW da Horda", "Sanitize prompts (recommended)": "Solicitações de higienização (recomendado)", "Automatically adjust generation parameters to ensure free image generations.": "Ajuste automaticamente os parâmetros de geração para garantir gerações de imagens livres.", "Avoid spending Anlas": "<PERSON><PERSON><PERSON> gas<PERSON>", "Opus tier": "(Nível Opus)", "View my Anlas": "Ver meus Anlas", "These settings only apply to DALL-E 3": "Essas configurações se aplicam somente ao DALL-E 3", "Image Style": "<PERSON><PERSON><PERSON>", "Image Quality": "Qualidade da imagem", "Standard": "Padrão", "HD": "alta definição", "sd_comfy_url": "Exemplo: {{comfy_url}}", "Open workflow editor": "<PERSON><PERSON> o editor de fluxo de trabalho", "Create new workflow": "Criar novo fluxo de trabalho", "Delete workflow": "Excluir fluxo de trabalho", "Enhance": "<PERSON><PERSON><PERSON>", "Refine": "Refinar", "Decrisper": "<PERSON><PERSON><PERSON><PERSON>", "Sampling steps": "Etapas de amostragem ()", "Width": "Largura ()", "Height": "Altura ()", "Resolution": "Resolução", "Model": "<PERSON><PERSON>", "Sampling method": "Método de amostragem", "Karras (not all samplers supported)": "<PERSON><PERSON><PERSON> (nem todos os samplers são suportados)", "SMEA versions of samplers are modified to perform better at high resolution.": "As versões SMEA dos amostradores são modificadas para funcionar melhor em alta resolução.", "SMEA": "SMEA", "DYN variants of SMEA samplers often lead to more varied output, but may fail at very high resolutions.": "Variantes DYN de amostradores SMEA geralmente levam a resultados mais variados, mas podem falhar em resoluções muito altas.", "DYN": "DIN", "Scheduler": "Agendador", "Restore Faces": "Restaurar rostos", "Hires. Fix": "Contrata. Consertar", "Upscaler": "Aprimorador", "Upscale by": "Sofisticado por", "Denoising strength": "Força de remoção de ruído", "Hires steps (2nd pass)": "Contrata etapas (2ª passagem)", "Preset for prompt prefix and negative prompt": "Predefinição para prefixo de prompt e prompt negativo", "Style": "<PERSON><PERSON><PERSON>", "Save style": "<PERSON><PERSON> est<PERSON>", "Delete style": "Excluir estilo", "Common prompt prefix": "Prefixo de prompt comum", "sd_prompt_prefix_placeholder": "Use {prompt} para especificar onde o prompt gerado será inserido", "Negative common prompt prefix": "Prefixo de prompt comum negativo", "Character-specific prompt prefix": "Prefixo de prompt específico de caractere", "Won't be used in groups.": "Não será usado em grupos.", "sd_character_prompt_placeholder": "Quaisquer características que descrevam o personagem selecionado no momento. Será adicionado após um prefixo de prompt comum.\nExemplo: mulher, olhos verdes, cabelo castanho, camisa rosa", "Character-specific negative prompt prefix": "Prefixo de prompt negativo específico do caractere", "sd_character_negative_prompt_placeholder": "Quaisquer características que não deveriam aparecer para o personagem selecionado. Será adicionado após um prefixo de prompt comum negativo.\nExemplo: joias, sapa<PERSON>, óculos", "Shareable": "Compartil<PERSON><PERSON><PERSON>", "Image Prompt Templates": "Modelos de prompt de imagem", "Vectors Model Warning": "É recomendado limpar os vetores ao alterar o modelo no meio do chat. <PERSON><PERSON><PERSON> contr<PERSON>, levar<PERSON> a resultados abaixo da média.", "Translate files into English before processing": "Traduza os arquivos para o inglês antes de processá-los", "Manager Users": "Gerenciar usuários", "New User": "Novo usuário", "Status:": "Status:", "Created:": "Criada:", "Display Name:": "Nome de exibição:", "User Handle:": "Identificador do usuário:", "Password:": "Senha:", "Confirm Password:": "Confirme sua senha:", "This will create a new subfolder...": "Isso criará uma nova subpasta no diretório /data/ com o identificador do usuário como nome da pasta.", "Current Password:": "<PERSON><PERSON> atual:", "New Password:": "Nova Senha:", "Confirm New Password:": "Confirme a nova senha:", "Debug Warning": "As funções nesta categoria são apenas para usuários avançados. Não clique em nada se não tiver certeza das consequências.", "Execute": "Executar", "Are you sure you want to delete this user?": "Tem certeza de que deseja excluir este usuário?", "Deleting:": "Excluindo:", "Also wipe user data.": "Limpe também os dados do usuário.", "Warning:": "Aviso:", "This action is irreversible.": "Esta ação é irreversível.", "Type the user's handle below to confirm:": "Digite o identificador do usuário abaixo para confirmar:", "Import Characters": "Importar caracteres", "Enter the URL of the content to import": "Insira o URL do conteúdo a ser importado", "Supported sources:": "Fontes suportadas:", "char_import_1": "Personagem <PERSON> (link direto ou ID)", "char_import_example": "Exemplo:", "char_import_2": "<PERSON>b Lorebook (link direto ou ID)", "char_import_3": "Personagem JanitorAI (Link Direto ou UUID)", "char_import_4": "Caractere Pygmalion.chat (Link Direto ou UUID)", "char_import_5": "Personagem AICharacterCard.com (link direto ou ID)", "char_import_6": "Link PNG direto (consulte", "char_import_7": "para hosts permitidos)", "char_import_8": "Personagem RisuRealm (link direto)", "Supports importing multiple characters.": "Suporta importação de vários caracteres.", "Write each URL or ID into a new line.": "Escreva cada URL ou ID em uma nova linha.", "Export for character": "Exportar para personagem", "Export prompts for this character, including their order.": "Exportar prompts para este personagem, incluindo sua ordem.", "Export all": "Exportar tudo", "Export all your prompts to a file": "Exporte todos os seus prompts para um arquivo", "Insert prompt": "Inserir prompt", "Delete prompt": "Excluir prompt", "Import a prompt list": "Importar uma lista de prompts", "Export this prompt list": "Exportar esta lista de prompts", "Reset current character": "Redefinir personagem atual", "New prompt": "Novo prompt", "Prompts": "Prompts", "Total Tokens:": "Total de Tokens:", "prompt_manager_tokens": "<PERSON><PERSON><PERSON>", "Are you sure you want to reset your settings to factory defaults?": "Tem certeza de que deseja redefinir as configurações para os padrões de fábrica?", "Don't forget to save a snapshot of your settings before proceeding.": "Não se esqueça de salvar um instantâneo de suas configurações antes de continuar.", "Settings Snapshots": "Instantâneos de configurações", "Record a snapshot of your current settings.": "Grave um instantâneo de suas configurações atuais.", "Make a Snapshot": "Faça um instantâneo", "Restore this snapshot": "Restaurar este instantâneo", "Hi,": "Oi,", "To enable multi-account features, restart the SillyTavern server with": "Para habilitar recursos de múltiplas contas, reinicie o servidor SillyTavern com", "set to true in the config.yaml file.": "definido como verdadeiro no arquivo config.yaml.", "Account Info": "Informações da Conta", "To change your user avatar, use the buttons below or select a default persona in the Persona Management menu.": "Para alterar seu avatar de usuário, use os botões abaixo ou selecione uma persona padrão no menu Gerenciamento de Personas.", "Set your custom avatar.": "Defina seu avatar personalizado.", "Remove your custom avatar.": "Remova seu avatar personalizado.", "Handle:": "Lidar:", "This account is password protected.": "Esta conta é protegida por senha.", "This account is not password protected.": "Esta conta não é protegida por senha.", "Account Actions": "Ações da conta", "Change Password": "Alterar a senha", "Manage your settings snapshots.": "Gerencie seus instantâneos de configurações.", "Download a complete backup of your user data.": "Baixe um backup completo dos dados do seu usuário.", "Download Backup": "Baixar cópia de segurança", "Danger Zone": "Zona de perigo", "Reset your settings to factory defaults.": "Redefina suas configurações para os padrões de fábrica.", "Reset Settings": "Redefinir as configurações", "Wipe all user data and reset your account to factory settings.": "Limpe todos os dados do usuário e redefina sua conta para as configurações de fábrica.", "Reset Everything": "Redefinir tudo", "Reset Code:": "Reiniciar código:", "Want to update?": "Quer atualizar?", "How to start chatting?": "Como começar a conversar?", "Click _space": "Clique", "and select a": "e selecione um", "Chat API": "API de Chat", "and pick a character.": "e escolha um personagem.", "You can browse a list of bundled characters in the": "Você pode navegar por uma lista de personagens agrupados no", "Download Extensions & Assets": "Baixar extensões e ativos", "menu within": "menu dentro", "Confused or lost?": "Confuso ou perdido?", "click these icons!": "clique nesses ícones!", "in the chat bar": "na barra de chat", "SillyTavern Documentation Site": "Site de Documentação do SillyTavern", "Extras Installation Guide": "Guia de Instalação de Extras", "Still have questions?": "Ainda tem perguntas?", "Join the SillyTavern Discord": "Junte-se ao Discord do SillyTavern", "Post a GitHub issue": "Publicar um problema no GitHub", "Contact the developers": "Contatar os desenvolvedores"}