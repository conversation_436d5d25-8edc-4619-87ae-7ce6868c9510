<div id="memory_settings">
    <div class="inline-drawer">
        <div class="inline-drawer-toggle inline-drawer-header">
            <div class="flex-container alignitemscenter margin0">
                <b data-i18n="ext_sum_title">Summarize</b>
                <i id="summaryExtensionPopoutButton" class="fa-solid fa-window-restore menu_button margin0"></i>
            </div>
            <div class="inline-drawer-icon fa-solid fa-circle-chevron-down down"></div>
        </div>
        <div class="inline-drawer-content">
            <div id="summaryExtensionDrawerContents">
                <label for="summary_source" data-i18n="ext_sum_with">Summarize with:</label>
                <select id="summary_source">
                    <option value="main" data-i18n="ext_sum_main_api">Main API</option>
                    <option value="extras">Extras API</option>
                    <option value="webllm" data-i18n="ext_sum_webllm">WebLLM Extension</option>
                </select><br>

                <div class="flex-container justifyspacebetween alignitemscenter">
                    <span class="flex1" data-i18n="ext_sum_current_summary">Current summary:</span>
                    <div id="memory_restore" class="menu_button flex1 margin0" data-i18n="[title]ext_sum_restore_tip" title="Restore a previous summary; use repeatedly to clear summarization state for this chat.">
                        <span data-i18n="ext_sum_restore_previous">Restore Previous</span>
                    </div>
                </div>

                <textarea id="memory_contents" class="text_pole textarea_compact" rows="6" data-i18n="[placeholder]ext_sum_memory_placeholder" placeholder="Summary will be generated here..."></textarea>
                <div class="memory_contents_controls">
                    <div id="memory_force_summarize" data-summary-source="main,webllm" class="menu_button menu_button_icon" title="Trigger a summary update right now." data-i18n="[title]ext_sum_force_tip">
                        <i class="fa-solid fa-database"></i>
                        <span data-i18n="ext_sum_force_text">Summarize now</span>
                    </div>
                    <label for="memory_frozen" title="Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API)." data-i18n="[title]Disable automatic summary updates. While paused, the summary remains as-is. You can still force an update by pressing the Summarize now button (which is only available with the Main API)."><input id="memory_frozen" type="checkbox" /><span data-i18n="ext_sum_pause">Pause</span></label>
                    <label data-summary-source="main" for="memory_skipWIAN" title="Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN." data-i18n="[title]Omit World Info and Author's Note from text to be summarized. Only has an effect when using the Main API. The Extras API always omits WI/AN.">
                        <input id="memory_skipWIAN" type="checkbox" />
                        <span data-i18n="ext_sum_no_wi_an">No WI/AN</span>
                    </label>
                </div>
                <div class="memory_contents_controls">
                    <div id="summarySettingsBlockToggle" class="menu_button menu_button_icon" data-i18n="[title]ext_sum_settings_tip" title="Edit summarization prompt, insertion position, etc.">
                        <i class="fa-solid fa-cog"></i>
                        <span data-i18n="ext_sum_settings">Summary Settings</span>
                    </div>
                </div>
                <div id="summarySettingsBlock" style="display:none;">
                    <div data-summary-source="main">
                        <label data-i18n="ext_sum_prompt_builder">
                            Prompt builder
                        </label>
                        <label class="checkbox_label" for="memory_prompt_builder_raw_blocking" data-i18n="[title]ext_sum_prompt_builder_1_desc" title="Extension will build its own prompt using messages that were not summarized yet. Blocks the chat until the summary is generated.">
                            <input id="memory_prompt_builder_raw_blocking" type="radio" name="memory_prompt_builder" value="1" />
                            <span data-i18n="ext_sum_prompt_builder_1">Raw, blocking</span>
                        </label>
                        <label class="checkbox_label" for="memory_prompt_builder_raw_non_blocking" data-i18n="[title]ext_sum_prompt_builder_2_desc" title="Extension will build its own prompt using messages that were not summarized yet. Does not block the chat while the summary is being generated. Not all backends support this mode.">
                            <input id="memory_prompt_builder_raw_non_blocking" type="radio" name="memory_prompt_builder" value="2" />
                            <span data-i18n="ext_sum_prompt_builder_2">Raw, non-blocking</span>
                        </label>
                        <label class="checkbox_label" id="memory_prompt_builder_default" data-i18n="[title]ext_sum_prompt_builder_3_desc" title="Extension will use the regular main prompt builder and add the summary request to it as the last system message.">
                            <input id="memory_prompt_builder_default" type="radio" name="memory_prompt_builder" value="0" />
                            <span data-i18n="ext_sum_prompt_builder_3">Classic, blocking</span>
                        </label>
                    </div>
                    <div data-summary-source="main,webllm">
                        <label for="memory_prompt" class="title_restorable">
                            <span data-i18n="Summary Prompt">Summary Prompt</span>
                            <div id="memory_prompt_restore" data-i18n="[title]ext_sum_restore_default_prompt_tip" title="Restore default prompt" class="right_menu_button">
                                <div class="fa-solid fa-clock-rotate-left"></div>
                            </div>
                        </label>
                        <textarea id="memory_prompt" class="text_pole textarea_compact" rows="6" data-i18n="[placeholder]ext_sum_prompt_placeholder" placeholder="This prompt will be sent to AI to request the summary generation. &lcub;&lcub;words&rcub;&rcub; will resolve to the 'Number of words' parameter."></textarea>
                        <label for="memory_prompt_words"><span data-i18n="ext_sum_target_length_1">Target summary length</span> <span data-i18n="ext_sum_target_length_2">(</span><span id="memory_prompt_words_value"></span><span data-i18n="ext_sum_target_length_3"> words)</span></label>
                        <input id="memory_prompt_words" type="range" value="{{defaultSettings.promptWords}}" min="{{defaultSettings.promptMinWords}}" max="{{defaultSettings.promptMaxWords}}" step="{{defaultSettings.promptWordsStep}}" />
                        <label for="memory_override_response_length">
                            <span data-i18n="ext_sum_api_response_length_1">API response length</span> <span data-i18n="ext_sum_api_response_length_2">(</span><span id="memory_override_response_length_value"></span><span data-i18n="ext_sum_api_response_length_3"> tokens)</span>
                            <small class="memory_disabled_hint" data-i18n="ext_sum_0_default">0 = default</small>
                        </label>
                        <input id="memory_override_response_length" type="range" value="{{defaultSettings.overrideResponseLength}}" min="{{defaultSettings.overrideResponseLengthMin}}" max="{{defaultSettings.overrideResponseLengthMax}}" step="{{defaultSettings.overrideResponseLengthStep}}" />
                        <label for="memory_max_messages_per_request">
                            <span data-i18n="ext_sum_raw_max_msg">[Raw/WebLLM] Max messages per request</span> (<span id="memory_max_messages_per_request_value"></span>)
                            <small class="memory_disabled_hint" data-i18n="ext_sum_0_unlimited">0 = unlimited</small>
                        </label>
                        <input id="memory_max_messages_per_request" type="range" value="{{defaultSettings.maxMessagesPerRequest}}" min="{{defaultSettings.maxMessagesPerRequestMin}}" max="{{defaultSettings.maxMessagesPerRequestMax}}" step="{{defaultSettings.maxMessagesPerRequestStep}}" />
                        <h4 data-i18n="Update frequency" class="textAlignCenter">
                            Update frequency
                        </h4>
                        <label for="memory_prompt_interval" class="title_restorable">
                            <span>
                                <span data-i18n="ext_sum_update_every_messages_1">Update every</span> <span id="memory_prompt_interval_value"></span><span data-i18n="ext_sum_update_every_messages_2"> messages</span>
                                <small class="memory_disabled_hint" data-i18n="ext_sum_0_disable">0 = disable</small>
                            </span>
                            <div id="memory_prompt_interval_auto" data-i18n="[title]ext_sum_auto_adjust_desc" title="Try to automatically adjust the interval based on the chat metrics." class="right_menu_button">
                                <div class="fa-solid fa-wand-magic-sparkles"></div>
                            </div>
                        </label>
                        <input id="memory_prompt_interval" type="range" value="{{defaultSettings.promptInterval}}" min="{{defaultSettings.promptMinInterval}}" max="{{defaultSettings.promptMaxInterval}}" step="{{defaultSettings.promptIntervalStep}}" />
                        <label for="memory_prompt_words_force" class="title_restorable">
                            <span>
                                <span data-i18n="ext_sum_update_every_words_1">Update every</span> <span id="memory_prompt_words_force_value"></span><span data-i18n="ext_sum_update_every_words_2"> words</span>
                                <small class="memory_disabled_hint" data-i18n="ext_sum_0_disable">0 = disable</small>
                            </span>
                            <div id="memory_prompt_words_auto" data-i18n="[title]ext_sum_auto_adjust_desc" title="Try to automatically adjust the interval based on the chat metrics." class="right_menu_button">
                                <div class="fa-solid fa-wand-magic-sparkles"></div>
                            </div>
                        </label>
                        <input id="memory_prompt_words_force" type="range" value="{{defaultSettings.promptForceWords}}" min="{{defaultSettings.promptMinForceWords}}" max="{{defaultSettings.promptMaxForceWords}}" step="{{defaultSettings.promptForceWordsStep}}" />
                        <small data-i18n="ext_sum_both_sliders">If both sliders are non-zero, then both will trigger summary updates at their respective intervals.</small>
                        <hr>
                    </div>
                    <div class="memory_template">
                        <label for="memory_template" data-i18n="ext_sum_injection_template">Injection Template</label>
                        <textarea id="memory_template" class="text_pole textarea_compact" rows="2" data-i18n="[placeholder]ext_sum_memory_template_placeholder" placeholder="&lcub;&lcub;summary&rcub;&rcub; will resolve to the current summary contents."></textarea>
                    </div>
                    <label for="memory_position" data-i18n="ext_sum_injection_position">Injection Position</label>
                    <label class="checkbox_label" for="memory_include_wi_scan" data-i18n="[title]ext_sum_include_wi_scan_desc" title="Include the latest summary in the WI scan.">
                        <input id="memory_include_wi_scan" type="checkbox" />
                        <span data-i18n="ext_sum_include_wi_scan">Include in World Info Scanning</span>
                    </label>
                    <div class="radio_group">
                        <label>
                            <input type="radio" name="memory_position" value="-1" />
                            <span data-i18n="None (not injected)">None (not injected)</span>
                            <i class="fa-solid fa-info-circle" title="The summary will not be injected into the prompt. You can still access it via the &lcub;&lcub;summary&rcub;&rcub; macro." data-i18n="[title]ext_sum_injection_position_none"></i>
                        </label>
                        <label>
                            <input type="radio" name="memory_position" value="2" />
                            <span data-i18n="Before Main Prompt / Story String">Before Main Prompt / Story String</span>
                        </label>
                        <label>
                            <input type="radio" name="memory_position" value="0" />
                            <span data-i18n="After Main Prompt / Story String">After Main Prompt / Story String</span>
                        </label>
                        <label class="flex-container alignItemsCenter" title="How many messages before the current end of the chat." data-i18n="[title]How many messages before the current end of the chat.">
                            <input type="radio" name="memory_position" value="1" />
                            <span data-i18n="In-chat @ Depth">In-chat @ Depth</span> <input id="memory_depth" class="text_pole widthUnset" type="number" min="0" max="999" />
                            <span data-i18n="as">as</span>
                            <select id="memory_role" class="text_pole widthNatural">
                                <option value="0" data-i18n="System">System</option>
                                <option value="1" data-i18n="User">User</option>
                                <option value="2" data-i18n="Assistant">Assistant</option>
                            </select>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
